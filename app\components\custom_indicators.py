"""
Custom Indicators and Intermarket Analysis Module

This module provides:
- Custom indicator creation with AI-powered optimization
- Intermarket analysis (currency, commodity impacts)
- Options flow analysis simulation
- Advanced technical indicators
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class CustomIndicators:
    """
    Custom Technical Indicators with AI-powered optimization
    """
    
    def __init__(self):
        self.custom_indicators = {}
        self.optimization_history = {}
    
    @staticmethod
    def create_adaptive_moving_average(data: pd.Series, period: int = 20, alpha: float = 0.1) -> pd.Series:
        """
        Create an adaptive moving average that adjusts to market volatility
        
        Args:
            data (pd.Series): Price data
            period (int): Base period for calculation
            alpha (float): Adaptation factor
            
        Returns:
            pd.Series: Adaptive moving average
        """
        try:
            # Calculate volatility
            volatility = data.rolling(period).std()
            
            # Adaptive factor based on volatility
            adaptive_factor = alpha * (volatility / volatility.rolling(period * 2).mean())
            adaptive_factor = adaptive_factor.fillna(alpha)
            
            # Calculate adaptive EMA
            ama = pd.Series(index=data.index, dtype=float)
            ama.iloc[0] = data.iloc[0]
            
            for i in range(1, len(data)):
                ama.iloc[i] = ama.iloc[i-1] + adaptive_factor.iloc[i] * (data.iloc[i] - ama.iloc[i-1])
            
            return ama
            
        except Exception as e:
            logger.error(f"Error creating adaptive moving average: {str(e)}")
            return data.rolling(period).mean()
    
    @staticmethod
    def create_ai_momentum_indicator(data: pd.DataFrame, lookback: int = 14) -> Dict[str, pd.Series]:
        """
        Create AI-powered momentum indicator combining multiple momentum measures
        
        Args:
            data (pd.DataFrame): OHLCV data
            lookback (int): Lookback period
            
        Returns:
            Dict[str, pd.Series]: AI momentum components
        """
        try:
            close = data['Close']
            high = data['High']
            low = data['Low']
            volume = data.get('Volume', pd.Series([1] * len(data)))
            
            # Traditional momentum
            momentum = close.pct_change(lookback)
            
            # Rate of Change
            roc = (close - close.shift(lookback)) / close.shift(lookback) * 100
            
            # Relative Strength Index
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=lookback).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=lookback).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            # Volume-weighted momentum
            volume_momentum = (close.pct_change() * volume).rolling(lookback).sum() / volume.rolling(lookback).sum()
            
            # Price efficiency
            price_change = abs(close - close.shift(lookback))
            price_path = abs(close.diff()).rolling(lookback).sum()
            efficiency = price_change / price_path
            
            # Combine indicators using AI weighting
            weights = CustomIndicators._calculate_ai_weights([momentum, roc/100, (rsi-50)/50, volume_momentum, efficiency])
            
            ai_momentum = (
                weights[0] * momentum +
                weights[1] * (roc/100) +
                weights[2] * ((rsi-50)/50) +
                weights[3] * volume_momentum +
                weights[4] * efficiency
            )
            
            return {
                'AI_Momentum': ai_momentum,
                'Traditional_Momentum': momentum,
                'ROC': roc,
                'RSI': rsi,
                'Volume_Momentum': volume_momentum,
                'Price_Efficiency': efficiency,
                'Weights': pd.Series(weights, index=['Momentum', 'ROC', 'RSI', 'Volume', 'Efficiency'])
            }
            
        except Exception as e:
            logger.error(f"Error creating AI momentum indicator: {str(e)}")
            return {'AI_Momentum': close.pct_change(lookback)}
    
    @staticmethod
    def _calculate_ai_weights(indicators: List[pd.Series]) -> List[float]:
        """Calculate AI-optimized weights for combining indicators"""
        try:
            # Simple correlation-based weighting
            weights = []
            
            for i, indicator in enumerate(indicators):
                if indicator.isna().all():
                    weights.append(0.0)
                    continue
                
                # Calculate indicator quality metrics
                volatility = indicator.std()
                trend_strength = abs(indicator.rolling(20).mean().iloc[-1]) if len(indicator) >= 20 else 0
                
                # Weight based on trend strength and inverse volatility
                weight = trend_strength / (volatility + 1e-6) if volatility > 0 else 0
                weights.append(weight)
            
            # Normalize weights
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(indicators)] * len(indicators)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error calculating AI weights: {str(e)}")
            return [1.0 / len(indicators)] * len(indicators)
    
    @staticmethod
    def create_volatility_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        Create adaptive volatility bands
        
        Args:
            data (pd.Series): Price data
            period (int): Period for calculation
            std_dev (float): Standard deviation multiplier
            
        Returns:
            Dict[str, pd.Series]: Volatility bands
        """
        try:
            # Calculate base moving average
            ma = data.rolling(period).mean()
            
            # Calculate adaptive standard deviation
            rolling_std = data.rolling(period).std()
            
            # Create bands
            upper_band = ma + (std_dev * rolling_std)
            lower_band = ma - (std_dev * rolling_std)
            
            # Calculate band width and position
            band_width = (upper_band - lower_band) / ma * 100
            band_position = (data - lower_band) / (upper_band - lower_band) * 100
            
            return {
                'Middle_Band': ma,
                'Upper_Band': upper_band,
                'Lower_Band': lower_band,
                'Band_Width': band_width,
                'Band_Position': band_position
            }
            
        except Exception as e:
            logger.error(f"Error creating volatility bands: {str(e)}")
            return {'Middle_Band': data.rolling(period).mean()}

class IntermarketAnalysis:
    """
    Intermarket Analysis for currency and commodity impacts
    """
    
    def __init__(self):
        self.correlation_cache = {}
        self.impact_factors = {}
    
    @staticmethod
    def simulate_currency_impact(stock_data: pd.DataFrame, currency_strength: float = 0.0) -> Dict[str, Any]:
        """
        Simulate currency impact on stock prices
        
        Args:
            stock_data (pd.DataFrame): Stock price data
            currency_strength (float): Currency strength indicator (-1 to 1)
            
        Returns:
            Dict[str, Any]: Currency impact analysis
        """
        try:
            close_prices = stock_data['Close']
            
            # Simulate currency correlation (Egyptian stocks typically have inverse correlation with USD strength)
            currency_correlation = -0.3  # Negative correlation assumption
            
            # Calculate currency-adjusted prices
            currency_factor = 1 + (currency_strength * currency_correlation * 0.1)
            adjusted_prices = close_prices * currency_factor
            
            # Calculate impact metrics
            price_impact = (adjusted_prices.iloc[-1] - close_prices.iloc[-1]) / close_prices.iloc[-1] * 100
            volatility_impact = adjusted_prices.std() / close_prices.std() - 1
            
            return {
                'currency_correlation': currency_correlation,
                'price_impact_percent': price_impact,
                'volatility_impact': volatility_impact,
                'adjusted_current_price': adjusted_prices.iloc[-1],
                'currency_factor': currency_factor,
                'recommendation': IntermarketAnalysis._get_currency_recommendation(price_impact)
            }
            
        except Exception as e:
            logger.error(f"Error simulating currency impact: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def simulate_commodity_impact(stock_data: pd.DataFrame, commodity_prices: Dict[str, float] = None) -> Dict[str, Any]:
        """
        Simulate commodity impact on stock prices
        
        Args:
            stock_data (pd.DataFrame): Stock price data
            commodity_prices (Dict[str, float]): Commodity price changes
            
        Returns:
            Dict[str, Any]: Commodity impact analysis
        """
        try:
            if commodity_prices is None:
                commodity_prices = {
                    'oil': 0.0,      # Oil price change %
                    'gold': 0.0,     # Gold price change %
                    'copper': 0.0    # Copper price change %
                }
            
            close_prices = stock_data['Close']
            
            # Simulate commodity correlations for Egyptian market
            correlations = {
                'oil': 0.2,      # Positive correlation (energy sector)
                'gold': 0.1,     # Slight positive correlation
                'copper': 0.15   # Industrial correlation
            }
            
            # Calculate weighted commodity impact
            total_impact = 0
            impact_breakdown = {}
            
            for commodity, price_change in commodity_prices.items():
                if commodity in correlations:
                    correlation = correlations[commodity]
                    impact = price_change * correlation * 0.01  # Convert to decimal
                    total_impact += impact
                    impact_breakdown[commodity] = {
                        'price_change': price_change,
                        'correlation': correlation,
                        'impact': impact * 100  # Convert back to percentage
                    }
            
            # Apply impact to stock prices
            commodity_factor = 1 + total_impact
            adjusted_prices = close_prices * commodity_factor
            
            # Calculate metrics
            price_impact = total_impact * 100
            
            return {
                'total_price_impact_percent': price_impact,
                'commodity_factor': commodity_factor,
                'adjusted_current_price': adjusted_prices.iloc[-1],
                'impact_breakdown': impact_breakdown,
                'recommendation': IntermarketAnalysis._get_commodity_recommendation(price_impact)
            }
            
        except Exception as e:
            logger.error(f"Error simulating commodity impact: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_currency_recommendation(price_impact: float) -> str:
        """Get recommendation based on currency impact"""
        if price_impact > 2:
            return "Strong positive currency impact - Consider buying"
        elif price_impact > 0.5:
            return "Moderate positive currency impact - Cautiously bullish"
        elif price_impact < -2:
            return "Strong negative currency impact - Consider selling"
        elif price_impact < -0.5:
            return "Moderate negative currency impact - Cautiously bearish"
        else:
            return "Minimal currency impact - Neutral"
    
    @staticmethod
    def _get_commodity_recommendation(price_impact: float) -> str:
        """Get recommendation based on commodity impact"""
        if price_impact > 1.5:
            return "Positive commodity tailwinds - Bullish outlook"
        elif price_impact > 0.3:
            return "Mild commodity support - Slightly positive"
        elif price_impact < -1.5:
            return "Commodity headwinds - Bearish pressure"
        elif price_impact < -0.3:
            return "Mild commodity pressure - Slightly negative"
        else:
            return "Neutral commodity environment"

class OptionsFlowAnalysis:
    """
    Options Flow Analysis (Simulated for Egyptian market)
    """
    
    @staticmethod
    def simulate_options_flow(stock_data: pd.DataFrame, volume_data: pd.Series = None) -> Dict[str, Any]:
        """
        Simulate options flow analysis based on volume and price patterns
        
        Args:
            stock_data (pd.DataFrame): Stock price data
            volume_data (pd.Series): Volume data
            
        Returns:
            Dict[str, Any]: Simulated options flow analysis
        """
        try:
            close_prices = stock_data['Close']
            volume = volume_data if volume_data is not None else stock_data.get('Volume', pd.Series([1] * len(stock_data)))
            
            # Simulate unusual activity detection
            avg_volume = volume.rolling(20).mean()
            volume_spike = volume / avg_volume
            
            # Simulate call/put ratio based on price momentum and volume
            price_momentum = close_prices.pct_change(5)
            
            # Higher volume + positive momentum = more call activity
            call_activity = np.where(
                (volume_spike > 1.5) & (price_momentum > 0.02),
                volume_spike * price_momentum * 100,
                0
            )
            
            # Higher volume + negative momentum = more put activity
            put_activity = np.where(
                (volume_spike > 1.5) & (price_momentum < -0.02),
                volume_spike * abs(price_momentum) * 100,
                0
            )
            
            # Calculate metrics
            total_call_activity = np.sum(call_activity[-5:])  # Last 5 days
            total_put_activity = np.sum(put_activity[-5:])    # Last 5 days
            
            call_put_ratio = total_call_activity / (total_put_activity + 1e-6)
            
            # Unusual activity score
            unusual_activity_score = max(volume_spike.iloc[-5:])
            
            return {
                'call_put_ratio': call_put_ratio,
                'total_call_activity': total_call_activity,
                'total_put_activity': total_put_activity,
                'unusual_activity_score': unusual_activity_score,
                'sentiment': OptionsFlowAnalysis._get_options_sentiment(call_put_ratio),
                'activity_level': OptionsFlowAnalysis._get_activity_level(unusual_activity_score),
                'recommendation': OptionsFlowAnalysis._get_options_recommendation(call_put_ratio, unusual_activity_score)
            }
            
        except Exception as e:
            logger.error(f"Error simulating options flow: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_options_sentiment(call_put_ratio: float) -> str:
        """Determine sentiment based on call/put ratio"""
        if call_put_ratio > 2.0:
            return "Very Bullish"
        elif call_put_ratio > 1.2:
            return "Bullish"
        elif call_put_ratio > 0.8:
            return "Neutral"
        elif call_put_ratio > 0.5:
            return "Bearish"
        else:
            return "Very Bearish"
    
    @staticmethod
    def _get_activity_level(score: float) -> str:
        """Determine activity level"""
        if score > 3.0:
            return "Extremely High"
        elif score > 2.0:
            return "High"
        elif score > 1.5:
            return "Moderate"
        else:
            return "Normal"
    
    @staticmethod
    def _get_options_recommendation(call_put_ratio: float, activity_score: float) -> str:
        """Get recommendation based on options flow"""
        if call_put_ratio > 1.5 and activity_score > 2.0:
            return "Strong bullish options flow - Consider long positions"
        elif call_put_ratio < 0.7 and activity_score > 2.0:
            return "Strong bearish options flow - Consider short positions"
        elif activity_score > 2.5:
            return "High unusual activity - Monitor closely for breakout"
        else:
            return "Normal options activity - No strong directional signal"
