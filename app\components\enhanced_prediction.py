"""
Enhanced prediction component for the AI Stocks Bot app
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import os
import logging
import time
from typing import Dict, List, Optional, Union, Tuple, Any

# Import our modules
from app.models.predict import predict_future_prices
from app.utils.state_manager import load_stock_data
from app.components.status_indicator import status_indicator, timed_operation, multi_step_operation
from scrapers.price_scraper import PriceScraper

# Import advanced prediction modules
try:
    from app.models.hybrid_predict import hybrid_predict_future_prices
    from app.utils.trend_analysis import get_trend_analysis, TREND_ANALYSIS_AVAILABLE
    from app.utils.performance import update_model_performance, get_model_performance, get_best_model
    HYBRID_PREDICTIONS_AVAILABLE = True
except ImportError:
    HYBRID_PREDICTIONS_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

def enhanced_prediction_component(historical_data=None, live_data=None, symbol=None):
    """
    Enhanced Streamlit component for making predictions with improved UI

    Args:
        historical_data (pd.DataFrame): Historical stock data
        live_data (pd.DataFrame): Live stock data
        symbol (str): Stock symbol
    """
    # Check if we have data
    if historical_data is None or symbol is None:
        st.warning("Please select a stock first")
        return

    # Create a container for the prediction interface
    prediction_container = st.container()

    with prediction_container:
        # Create a layout with columns
        col1, col2 = st.columns([2, 1])

        with col1:
            st.subheader("Prediction Settings")

            # Model selection
            model_options = ["LSTM", "BiLSTM", "RandomForest", "GradientBoosting", "Ensemble"]

            # Add Hybrid model option if available
            if HYBRID_PREDICTIONS_AVAILABLE:
                model_options.append("Hybrid")

            selected_model = st.selectbox(
                "Select prediction model",
                options=model_options,
                index=model_options.index("Ensemble") if "Ensemble" in model_options else 0,
                help="Hybrid model combines multiple techniques including statistical models, ML, deep learning, and trend analysis"
            )

            # Horizon selection
            horizon_options = {
                "Short-term": [4, 15, 30, 60],
                "Medium-term": [60, 120, 240, 480],
                "Long-term": [480, 960, 1440, 2880]  # Up to 2 days
            }

            horizon_type = st.radio(
                "Prediction timeframe",
                options=list(horizon_options.keys()),
                horizontal=True
            )

            selected_horizons = st.multiselect(
                "Select prediction horizons (minutes)",
                options=horizon_options[horizon_type],
                default=[horizon_options[horizon_type][0], horizon_options[horizon_type][-1]]
            )

            # Live data option
            use_live_data = st.toggle(
                "Use live data for current price",
                value=True,
                help="Fetch the latest price from TradingView before making predictions"
            )

            # Advanced options in an expander
            with st.expander("Advanced Options"):
                # Confidence intervals
                show_confidence = st.toggle(
                    "Show confidence intervals",
                    value=True,
                    help="Display prediction uncertainty ranges"
                )

                # Ensemble options if ensemble is selected
                if selected_model == "Ensemble":
                    ensemble_models = st.multiselect(
                        "Select models for ensemble",
                        options=["LSTM", "BiLSTM", "RandomForest", "GradientBoosting", "LinearRegression"],
                        default=["LSTM", "RandomForest", "GradientBoosting"]
                    )

                    weighting_method = st.radio(
                        "Ensemble weighting method",
                        options=["Equal", "Performance-based", "Adaptive"],
                        index=2,
                        horizontal=True
                    )

                # Feature importance
                show_feature_importance = st.toggle(
                    "Show feature importance",
                    value=False,
                    help="Display which features most influenced the prediction"
                )

        with col2:
            st.subheader("Run Prediction")

            # Create a card-like container for the prediction button
            prediction_card = st.container()

            with prediction_card:
                # Add some styling to make it look like a card
                st.markdown("""
                <style>
                div.stButton > button {
                    width: 100%;
                    height: 3em;
                    font-size: 1.2em;
                    font-weight: bold;
                    margin-bottom: 1em;
                }
                </style>
                """, unsafe_allow_html=True)

                # Add the prediction button
                predict_button = st.button("🔮 Generate Predictions", use_container_width=True)

            # Add a quick summary of the current settings
            st.markdown("### Current Settings")
            st.markdown(f"**Model:** {selected_model}")
            st.markdown(f"**Timeframe:** {horizon_type}")
            st.markdown(f"**Horizons:** {', '.join(map(str, selected_horizons))} minutes")
            st.markdown(f"**Live Data:** {'Enabled' if use_live_data else 'Disabled'}")

            # Add a section for recent predictions
            st.markdown("### Recent Predictions")

            # Check if we have any recent predictions in session state
            if 'recent_predictions' not in st.session_state:
                st.session_state.recent_predictions = []

            # Display recent predictions
            if not st.session_state.recent_predictions:
                st.info("No recent predictions. Generate a prediction to see it here.")
            else:
                # Show the most recent prediction
                latest_pred = st.session_state.recent_predictions[-1]
                # Access 'predicted_value' instead of 'price'
                st.success(f"Latest prediction ({latest_pred['time'].strftime('%H:%M:%S')}): {latest_pred['horizon']} min → {latest_pred['predicted_value']:.2f}")

    # Handle prediction generation
    if predict_button:
        # Define the steps for our multi-step operation
        prediction_steps = [
            {
                'name': 'Prepare data',
                'func': lambda: historical_data
            }
        ]

        # Add live data step if enabled
        if use_live_data:
            prediction_steps.append({
                'name': 'Fetch live price data',
                'func': fetch_live_data,
                'args': [symbol]
            })

        # Add prediction step
        prediction_steps.append({
            'name': 'Generate predictions',
            'func': generate_predictions,
            'args': [historical_data, selected_model, selected_horizons, symbol, live_data if use_live_data else None]
        })

        # The actual visualization is handled by display_predictions after the multi-step operation
        # Removing this placeholder step to avoid the TypeError
        # prediction_steps.append({
        #     'name': 'Visualize results',
        #     'func': lambda x: x  # This will be handled separately
        # })

        # Execute the multi-step operation
        results = multi_step_operation(prediction_steps, title="Generating predictions...")

        # Process the results
        if results and len(results) > 2 and results[2] is not None:  # Check if predictions were generated
            predictions = results[2]

            # Validate predictions
            if not isinstance(predictions, dict) or not predictions:
                st.error("No valid predictions were generated. Please check if models are trained for the selected horizons.")
                st.info("Go to the Train Model page to train models for your selected horizons.")
                return

            # Store in session state for recent predictions
            current_time = datetime.now()
            for horizon, price in predictions.items():
                if isinstance(price, dict):  # Multi-point predictions
                    # Get the first prediction point
                    first_time = list(price.keys())[0]
                    first_price = price[first_time]

                    # Create prediction record
                    prediction_record = {
                        'time': current_time,
                        'horizon': horizon,
                        'predicted_value': first_price,
                        'actual_value': None  # Will be updated later when actual value is known
                    }

                    # Add to recent predictions
                    st.session_state.recent_predictions.append(prediction_record)

                    # Add to prediction history for performance tracking
                    if 'prediction_history' not in st.session_state:
                        st.session_state.prediction_history = []

                    st.session_state.prediction_history.append(prediction_record)
                else:  # Single point predictions
                    # Create prediction record
                    prediction_record = {
                        'time': current_time,
                        'horizon': horizon,
                        'predicted_value': price,
                        'actual_value': None  # Will be updated later when actual value is known
                    }

                    # Add to recent predictions
                    st.session_state.recent_predictions.append(prediction_record)

                    # Add to prediction history for performance tracking
                    if 'prediction_history' not in st.session_state:
                        st.session_state.prediction_history = []

                    st.session_state.prediction_history.append(prediction_record)

            # Update model performance if we have actual values to compare with
            try:
                # Check if we have prediction history
                if 'prediction_history' in st.session_state:
                    # Get the current price
                    current_price = historical_data['Close'].iloc[-1]

                    # Update actual values for past predictions that have matured
                    updated_records = 0
                    for pred_record in st.session_state.prediction_history:
                        # Skip records that already have actual values
                        if pred_record.get('actual_value') is not None:
                            continue

                        # Check if the prediction has matured
                        pred_time = pred_record.get('time')
                        horizon = pred_record.get('horizon')
                        predicted_value = pred_record.get('predicted_value')

                        if pred_time and horizon and predicted_value is not None:
                            maturity_time = pred_time + timedelta(minutes=horizon)

                            # If the prediction has matured, update with actual value
                            if datetime.now() >= maturity_time:
                                # Validate the predicted value
                                if not isinstance(predicted_value, (int, float)):
                                    logger.warning(f"Invalid predicted value: {predicted_value}")
                                    continue

                                # Check for unrealistic predictions (more than 50% different from current price)
                                if current_price > 0 and predicted_value > 0:
                                    percent_diff = abs(predicted_value - current_price) / current_price * 100
                                    if percent_diff > 50:
                                        logger.warning(f"Unrealistic prediction detected: predicted={predicted_value}, actual={current_price}, diff={percent_diff:.2f}%")
                                        # Skip this record for performance tracking
                                        continue

                                # Update the record
                                pred_record['actual_value'] = current_price
                                updated_records += 1

                                # Update performance metrics
                                update_model_performance(
                                    symbol=symbol,
                                    model_name=selected_model.lower(),
                                    horizon=horizon,
                                    actual_value=current_price,
                                    predicted_value=predicted_value
                                )

                    if updated_records > 0:
                        logger.info(f"Updated {updated_records} performance records for {selected_model}")
            except Exception as e:
                logger.error(f"Error updating model performance: {str(e)}")

            # Keep only the 5 most recent predictions
            if len(st.session_state.recent_predictions) > 5:
                st.session_state.recent_predictions = st.session_state.recent_predictions[-5:]

            # Display the predictions
            display_predictions(historical_data, live_data if use_live_data and results[1] is not None else None,
                               predictions, symbol, show_confidence)

            # Show feature importance if requested
            if show_feature_importance:
                display_feature_importance(selected_model)
        else:
            # Handle case when predictions fail
            st.error("Failed to generate predictions. This could be due to:")
            st.markdown("""
            - **No trained models** for the selected horizons
            - **Data processing errors**
            - **Model loading issues**

            **Solutions:**
            1. Go to the **Train Model** page and train models for your selected horizons
            2. Check if the stock symbol is correct
            3. Ensure historical data is loaded properly
            4. Try with different horizons or model types
            """)

            # Add a button to go to train model page
            if st.button("🔧 Go to Train Model Page", type="primary"):
                st.session_state.page = "Train Model"
                st.rerun()

def fetch_live_data(symbol):
    """Fetch live price data for a symbol"""
    try:
        # Initialize scraper
        scraper = PriceScraper(source="tradingview")

        # Get live data
        price_data = scraper.get_price(symbol)

        if price_data:
            # Create DataFrame
            live_data = pd.DataFrame([price_data])
            return live_data
        else:
            st.warning("Could not fetch live price. Using historical data instead.")
            return None
    except Exception as e:
        st.error(f"Error fetching live data: {str(e)}")
        return None

def generate_predictions(historical_data, model_type, horizons, symbol, live_data=None):
    """Generate predictions using the selected model"""
    try:
        # Validate inputs
        if not horizons:
            st.error("No horizons selected for prediction.")
            return None

        if historical_data is None or historical_data.empty:
            st.error("No historical data available for prediction.")
            return None

        if not symbol:
            st.error("No stock symbol provided.")
            return None

        # Convert model type to lowercase for compatibility
        model_type = model_type.lower()

        # Map UI model names to internal model types
        model_map = {
            "lstm": "lstm",
            "bilstm": "bilstm",
            "randomforest": "rf",
            "gradientboosting": "gb",
            "ensemble": "ensemble"
        }

        # Get the internal model type
        internal_model = model_map.get(model_type, "ensemble")

        # Check if models are trained for the selected horizons
        from app.utils.data_processing import is_model_trained
        missing_models = []
        for horizon in horizons:
            if not is_model_trained(symbol, horizon, internal_model, 'saved_models', 'minutes'):
                missing_models.append(horizon)

        if missing_models:
            st.error(f"No trained models found for horizons: {missing_models} minutes with {model_type} model.")
            st.info("Please train models for these horizons on the Train Model page.")
            return None

        # Check if hybrid predictions are available
        if HYBRID_PREDICTIONS_AVAILABLE and (model_type == "hybrid" or model_type == "ensemble"):
            if model_type == "hybrid":
                st.info("Using advanced hybrid prediction model with trend analysis")

                # Generate predictions using the hybrid approach with all techniques
                predictions = hybrid_predict_future_prices(
                    historical_data,
                    symbol,
                    horizons=horizons,
                    use_statistical=True,
                    use_ml=True,
                    use_deep_learning=True,
                    use_trend_analysis=True,
                    blend_method='weighted'
                )
            else:  # ensemble
                # Generate predictions using the hybrid approach but with limited techniques
                predictions = hybrid_predict_future_prices(
                    historical_data,
                    symbol,
                    horizons=horizons,
                    use_statistical=False,  # Don't use statistical models for regular ensemble
                    use_ml=True,
                    use_deep_learning=True,
                    use_trend_analysis=False,  # Don't use trend analysis for regular ensemble
                    blend_method='weighted'
                )

            # Add trend analysis information to session state for display
            if TREND_ANALYSIS_AVAILABLE:
                trend_info = get_trend_analysis(historical_data)
                st.session_state.trend_analysis = trend_info

                # Log trend information
                logger.info(f"Trend analysis for {symbol}: {trend_info.get('trend_direction', 'unknown')} "
                           f"(strength: {trend_info.get('trend_strength', 0):.2f})")
        else:
            # Use the standard prediction function
            # If we have live data, use predict_from_live_data instead
            if live_data is not None and not live_data.empty:
                try:
                    from models.predict import predict_from_live_data
                    predictions = predict_from_live_data(
                        live_data,
                        historical_data,
                        symbol,
                        horizons=horizons,
                        model_type=internal_model
                    )
                except Exception as e:
                    logger.warning(f"Error using live data for prediction: {str(e)}")
                    # Fall back to standard prediction
                    predictions = predict_future_prices(
                        historical_data,
                        symbol,
                        horizons=horizons,
                        model_type=internal_model
                    )
            else:
                predictions = predict_future_prices(
                    historical_data,
                    symbol,
                    horizons=horizons,
                    model_type=internal_model
                )

        return predictions
    except Exception as e:
        st.error(f"Error generating predictions: {str(e)}")
        logger.error(f"Error generating predictions: {str(e)}")
        return None

def display_predictions(historical_data, live_data, predictions, symbol, show_confidence=True):
    """Display the predictions in a visually appealing way"""
    st.subheader("Prediction Results")

    # Create a container for the results
    results_container = st.container()

    with results_container:
        # Create tabs for different views
        tab1, tab2, tab3, tab4 = st.tabs(["Chart View", "Table View", "Analysis", "Performance"])

        with tab1:
            # Create a Plotly figure for the predictions
            fig = go.Figure()

            # Add historical data
            fig.add_trace(go.Scatter(
                x=historical_data['Date'].iloc[-30:],  # Show last 30 days
                y=historical_data['Close'].iloc[-30:],
                mode='lines',
                name='Historical',
                line=dict(color='blue')
            ))

            # Add live data if available
            if live_data is not None and not live_data.empty:
                fig.add_trace(go.Scatter(
                    x=live_data['Date'],
                    y=live_data['Close'],
                    mode='markers',
                    name='Live',
                    marker=dict(color='green', size=10)
                ))

            # Add predictions
            colors = ['red', 'orange', 'purple', 'brown', 'pink']

            for i, (horizon, pred) in enumerate(predictions.items()):
                color = colors[i % len(colors)]

                if isinstance(pred, dict):  # Multi-point predictions
                    # Extract times and prices
                    pred_times = list(pred.keys())
                    pred_prices = list(pred.values())

                    fig.add_trace(go.Scatter(
                        x=pred_times,
                        y=pred_prices,
                        mode='lines+markers',
                        name=f'{horizon} min',
                        line=dict(color=color, dash='dot'),
                        marker=dict(color=color, size=8)
                    ))
                else:  # Single point prediction
                    # Create a single future point
                    last_time = historical_data['Date'].iloc[-1]
                    future_time = last_time + timedelta(minutes=horizon)

                    fig.add_trace(go.Scatter(
                        x=[last_time, future_time],
                        y=[historical_data['Close'].iloc[-1], pred],
                        mode='lines+markers',
                        name=f'{horizon} min',
                        line=dict(color=color, dash='dot'),
                        marker=dict(color=color, size=8)
                    ))

            # Update layout
            fig.update_layout(
                title=f'{symbol} Price Predictions',
                xaxis_title='Date',
                yaxis_title='Price',
                hovermode='x unified',
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
            )

            # Show the figure
            st.plotly_chart(fig, use_container_width=True)

        with tab2:
            # Create a table of predictions
            pred_data = []

            # Get current price
            if live_data is not None and not live_data.empty:
                current_price = live_data['Close'].iloc[-1]
            else:
                current_price = historical_data['Close'].iloc[-1]

            # Add rows for each prediction
            for horizon, pred in predictions.items():
                if isinstance(pred, dict):  # Multi-point predictions
                    # Get the first prediction point
                    first_time = list(pred.keys())[0]
                    first_price = pred[first_time]

                    pred_data.append({
                        "Horizon": f"{horizon} minutes",
                        "Predicted Price": f"${first_price:.2f}",
                        "Current Price": f"${current_price:.2f}",
                        "Change": f"{((first_price - current_price) / current_price * 100):.2f}%"
                    })
                else:  # Single point prediction
                    pred_data.append({
                        "Horizon": f"{horizon} minutes",
                        "Predicted Price": f"${pred:.2f}",
                        "Current Price": f"${current_price:.2f}",
                        "Change": f"{((pred - current_price) / current_price * 100):.2f}%"
                    })

            # Display the table
            if pred_data:
                st.table(pd.DataFrame(pred_data))
            else:
                st.warning("No valid predictions to display")

        with tab3:
            st.subheader("Prediction Analysis")

            # Add some analysis of the predictions
            if predictions:
                # Calculate trend direction
                trend_directions = []

                for horizon, pred in predictions.items():
                    if isinstance(pred, dict):  # Multi-point predictions
                        first_price = list(pred.values())[0]
                    else:  # Single point prediction
                        first_price = pred

                    if first_price > current_price:
                        trend_directions.append("up")
                    elif first_price < current_price:
                        trend_directions.append("down")
                    else:
                        trend_directions.append("neutral")

                # Determine overall trend
                up_count = trend_directions.count("up")
                down_count = trend_directions.count("down")
                neutral_count = trend_directions.count("neutral")

                if up_count > down_count and up_count > neutral_count:
                    trend = "Bullish"
                    color = "green"
                elif down_count > up_count and down_count > neutral_count:
                    trend = "Bearish"
                    color = "red"
                else:
                    trend = "Neutral"
                    color = "gray"

                # Display the trend
                st.markdown(f"<h3 style='color: {color};'>Overall Trend: {trend}</h3>", unsafe_allow_html=True)

                # Add a confidence meter
                confidence = (max(up_count, down_count, neutral_count) / len(trend_directions)) * 100
                st.progress(confidence / 100)
                st.caption(f"Confidence: {confidence:.1f}%")

                # Check if we have trend analysis information
                if 'trend_analysis' in st.session_state and st.session_state.trend_analysis:
                    trend_info = st.session_state.trend_analysis

                    # Create columns for technical indicators
                    col1, col2 = st.columns(2)

                    with col1:
                        st.markdown("### Technical Indicators")

                        # Display trend direction and strength
                        trend_direction = trend_info.get('trend_direction', 'unknown')
                        trend_strength = trend_info.get('trend_strength', 0)

                        if trend_direction == 'up':
                            st.markdown(f"🔼 **Trend Direction:** Uptrend (Strength: {trend_strength:.2f})")
                        elif trend_direction == 'down':
                            st.markdown(f"🔽 **Trend Direction:** Downtrend (Strength: {trend_strength:.2f})")
                        else:
                            st.markdown(f"➡️ **Trend Direction:** Neutral (Strength: {trend_strength:.2f})")

                        # Display momentum
                        momentum = trend_info.get('momentum', 0)
                        if momentum > 0.3:
                            st.markdown(f"🚀 **Momentum:** Strong Positive ({momentum:.2f})")
                        elif momentum > 0:
                            st.markdown(f"📈 **Momentum:** Positive ({momentum:.2f})")
                        elif momentum > -0.3:
                            st.markdown(f"📉 **Momentum:** Negative ({momentum:.2f})")
                        else:
                            st.markdown(f"💥 **Momentum:** Strong Negative ({momentum:.2f})")

                        # Display volatility
                        volatility = trend_info.get('volatility', 0)
                        if volatility > 0.3:
                            st.markdown(f"⚠️ **Volatility:** High ({volatility:.2f})")
                        elif volatility > 0.15:
                            st.markdown(f"⚠️ **Volatility:** Medium ({volatility:.2f})")
                        else:
                            st.markdown(f"✅ **Volatility:** Low ({volatility:.2f})")

                    with col2:
                        st.markdown("### Support & Resistance")

                        # Display support and resistance levels
                        support = trend_info.get('support', 0)
                        resistance = trend_info.get('resistance', 0)
                        last_price = trend_info.get('last_price', 0)

                        # Calculate distances
                        if last_price > 0:
                            support_distance = (last_price - support) / last_price * 100
                            resistance_distance = (resistance - last_price) / last_price * 100

                            st.markdown(f"⬇️ **Support:** {support:.2f} ({support_distance:.1f}% below)")
                            st.markdown(f"⬆️ **Resistance:** {resistance:.2f} ({resistance_distance:.1f}% above)")

                        # Display breakout information
                        breakout = trend_info.get('breakout', False)
                        breakout_direction = trend_info.get('breakout_direction', 'none')

                        if breakout and breakout_direction == 'up':
                            st.markdown("🚨 **Breakout Alert:** Bullish breakout detected")
                        elif breakout and breakout_direction == 'down':
                            st.markdown("🚨 **Breakdown Alert:** Bearish breakdown detected")

                        # Display volume trend
                        volume_trend = trend_info.get('volume_trend', 'stable')
                        if volume_trend == 'increasing':
                            st.markdown("📊 **Volume:** Increasing (confirms trend)")
                        elif volume_trend == 'decreasing':
                            st.markdown("📊 **Volume:** Decreasing (weakening trend)")
                        else:
                            st.markdown("📊 **Volume:** Stable")

                # Add some analysis text
                st.markdown("### Key Insights")

                if trend == "Bullish":
                    st.markdown("- The model predicts an upward trend in the stock price")
                    st.markdown("- Consider watching for confirmation signals before making decisions")
                    st.markdown("- Monitor volume to confirm the strength of the trend")

                    # Add additional insights if trend analysis is available
                    if 'trend_analysis' in st.session_state and st.session_state.trend_analysis:
                        trend_info = st.session_state.trend_analysis

                        if trend_info.get('trend_direction') == 'up' and trend_info.get('volume_trend') == 'increasing':
                            st.markdown("- **Strong Buy Signal:** Uptrend confirmed by increasing volume")

                        if trend_info.get('breakout', False) and trend_info.get('breakout_direction') == 'up':
                            st.markdown("- **Breakout Alert:** Price has broken above resistance, suggesting potential for continued upward movement")

                        if trend_info.get('resistance_distance', 100) < 2:
                            st.markdown("- **Caution:** Price is approaching resistance level, watch for possible rejection")

                elif trend == "Bearish":
                    st.markdown("- The model predicts a downward trend in the stock price")
                    st.markdown("- Consider watching for confirmation signals before making decisions")
                    st.markdown("- Be cautious of potential reversals if the stock is already down significantly")

                    # Add additional insights if trend analysis is available
                    if 'trend_analysis' in st.session_state and st.session_state.trend_analysis:
                        trend_info = st.session_state.trend_analysis

                        if trend_info.get('trend_direction') == 'down' and trend_info.get('volume_trend') == 'increasing':
                            st.markdown("- **Strong Sell Signal:** Downtrend confirmed by increasing volume")

                        if trend_info.get('breakout', False) and trend_info.get('breakout_direction') == 'down':
                            st.markdown("- **Breakdown Alert:** Price has broken below support, suggesting potential for continued downward movement")

                        if trend_info.get('support_distance', 100) < 2:
                            st.markdown("- **Caution:** Price is approaching support level, watch for possible bounce")
                else:
                    st.markdown("- The model predicts a sideways or mixed trend")
                    st.markdown("- Consider waiting for a clearer signal before making decisions")
                    st.markdown("- Look for breakout patterns that might indicate a new trend direction")

                    # Add additional insights if trend analysis is available
                    if 'trend_analysis' in st.session_state and st.session_state.trend_analysis:
                        trend_info = st.session_state.trend_analysis

                        if trend_info.get('volatility', 0) > 0.2:
                            st.markdown("- **High Volatility Alert:** Despite sideways trend, high volatility suggests a potential breakout soon")

                        if trend_info.get('support_distance', 100) < 5 or trend_info.get('resistance_distance', 100) < 5:
                            st.markdown("- **Range-Bound Trading:** Price is moving between support and resistance levels, consider range trading strategies")

        with tab4:
            # Import the model performance component
            try:
                from app.components.model_performance import model_performance_component

                # Display model performance metrics
                model_performance_component(symbol)
            except Exception as e:
                st.error(f"Error loading model performance component: {str(e)}")
                st.info("To view model performance metrics, predictions must be generated and evaluated over time.")

def display_feature_importance(model_type):
    """Display feature importance for the selected model"""
    st.subheader("Feature Importance")

    # This would normally come from the model, but we'll use placeholder data
    features = [
        "Close_1d", "Volume_1d", "RSI_14", "MACD", "EMA_9",
        "SMA_20", "Bollinger_Upper", "Bollinger_Lower", "ATR_14", "OBV"
    ]

    importance = [0.25, 0.18, 0.15, 0.12, 0.08, 0.07, 0.06, 0.04, 0.03, 0.02]

    # Create a DataFrame
    importance_df = pd.DataFrame({
        "Feature": features,
        "Importance": importance
    })

    # Sort by importance
    importance_df = importance_df.sort_values("Importance", ascending=False)

    # Create a bar chart
    fig = px.bar(
        importance_df,
        x="Importance",
        y="Feature",
        orientation='h',
        title=f"Feature Importance for {model_type}",
        color="Importance",
        color_continuous_scale="Blues"
    )

    # Update layout
    fig.update_layout(
        xaxis_title="Relative Importance",
        yaxis_title="Feature",
        yaxis=dict(autorange="reversed")  # Highest importance at the top
    )

    # Show the figure
    st.plotly_chart(fig, use_container_width=True)

    # Add an explanation
    st.markdown("""
    **Feature Importance Explanation:**

    The chart above shows which features had the most influence on the model's predictions.
    Features with higher importance values have a stronger effect on the predicted price.
    """)
