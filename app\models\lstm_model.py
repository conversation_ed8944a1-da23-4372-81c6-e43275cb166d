import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
import logging
import os
import warnings
from typing import Dict, Any, Tuple

# Configure logging first
logger = logging.getLogger(__name__)

# Try to import TensorFlow, but make it optional
TENSORFLOW_AVAILABLE = False
tf = None
Sequential = None
Dense = None
LSTM = None
Dropout = None
Adam = None

try:
    import tensorflow as tf  # type: ignore
    from tensorflow.keras.models import Sequential  # type: ignore
    from tensorflow.keras.layers import Dense, LSTM, Dropout  # type: ignore
    from tensorflow.keras.optimizers import Adam  # type: ignore
    TENSORFLOW_AVAILABLE = True
    logger.info("TensorFlow imported successfully for LSTM model")
except ImportError as e:
    warnings.warn(f"TensorFlow not available: {str(e)}. LSTM model will not be functional.")
    logger.warning(f"TensorFlow import failed: {str(e)}. LSTM model will not be functional.")
except Exception as e:
    warnings.warn(f"Error importing TensorFlow: {str(e)}. LSTM model will not be functional.")
    logger.warning(f"TensorFlow import error: {str(e)}. LSTM model will not be functional.")

from app.models.base_model import BaseModel
from app.utils.data_processor import create_sequences

class LSTMModel(BaseModel):
    """
    LSTM model for time series prediction.
    """

    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the LSTM model.

        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional model parameters
        """
        super().__init__(target_column=target_column)
        self.model_name = "LSTM"

        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            warnings.warn("TensorFlow not available. LSTM model will not be functional.")

        # Model parameters
        self.sequence_length = kwargs.get('sequence_length', 10)
        self.units = kwargs.get('units', 50)
        self.dropout = kwargs.get('dropout', 0.2)
        self.epochs = kwargs.get('epochs', 50)
        self.batch_size = kwargs.get('batch_size', 32)

        # Scalers
        self.feature_scaler = MinMaxScaler()
        self.target_scaler = MinMaxScaler()

        # Model
        self.model = None

    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the LSTM model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Make a copy to avoid modifying the original data
        df = data.copy()

        # Extract target
        target = df[[self.target_column]]

        # Create features (use OHLC prices as features)
        price_cols = ['Open', 'High', 'Low', 'Close']
        features = df[price_cols]

        return features, target

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the LSTM model.

        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters

        Returns:
            Dict[str, Any]: Training metrics
        """
        logger.info("Training LSTM model")

        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow not available. Cannot train LSTM model.")
            return {'error': 'TensorFlow not available'}

        # Override default parameters if provided
        self.sequence_length = kwargs.get('sequence_length', self.sequence_length)
        self.units = kwargs.get('units', self.units)
        self.dropout = kwargs.get('dropout', self.dropout)
        self.epochs = kwargs.get('epochs', self.epochs)
        self.batch_size = kwargs.get('batch_size', self.batch_size)

        try:
            # Preprocess data
            features, target = self.preprocess_data(data)

            # Scale features and target
            X = self.feature_scaler.fit_transform(features)
            y = self.target_scaler.fit_transform(target)

            # Create sequences
            X_seq, y_seq = create_sequences(X, y, self.sequence_length)

            # Split data for validation
            train_size = int(len(X_seq) * 0.8)
            X_train, X_val = X_seq[:train_size], X_seq[train_size:]
            y_train, y_val = y_seq[:train_size], y_seq[train_size:]

            # Build model
            self.model = Sequential()
            self.model.add(LSTM(self.units, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])))
            self.model.add(Dropout(self.dropout))
            self.model.add(LSTM(self.units))
            self.model.add(Dropout(self.dropout))
            self.model.add(Dense(1))

            # Compile model
            self.model.compile(
                optimizer=Adam(),
                loss='mse',
                metrics=['mae']
            )

            # Train model
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=self.epochs,
                batch_size=self.batch_size,
                verbose=1,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(
                        monitor='val_loss',
                        patience=10,
                        restore_best_weights=True
                    )
                ]
            )

            # Return training metrics
            return {
                'history': history.history
            }
        except Exception as e:
            logger.error(f"Error training LSTM model: {str(e)}")
            return {'error': str(e)}

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the LSTM model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            np.ndarray: Predicted values
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow not available. Cannot make predictions with LSTM model.")
            return np.full(len(data), np.nan)

        if self.model is None:
            logger.error("Model has not been trained yet")
            return np.full(len(data), np.nan)

        try:
            # Preprocess data
            features, _ = self.preprocess_data(data)

            # Scale features
            X = self.feature_scaler.transform(features)

            # Create sequences
            X_seq = []
            for i in range(len(X) - self.sequence_length + 1):
                X_seq.append(X[i:i+self.sequence_length])

            X_seq = np.array(X_seq)

            # Generate predictions
            predictions = self.model.predict(X_seq)

            # Inverse transform predictions
            predictions = self.target_scaler.inverse_transform(predictions)

            # Pad predictions to match input length
            padded_predictions = np.full(len(data), np.nan)
            padded_predictions[self.sequence_length-1:] = predictions.flatten()

            return padded_predictions
        except Exception as e:
            logger.error(f"Error making predictions with LSTM model: {str(e)}")
            return np.full(len(data), np.nan)

    def save(self, path: str) -> None:
        """
        Save the LSTM model to disk.

        Args:
            path (str): The path to save the model
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow not available. Cannot save LSTM model.")
            return

        if self.model is None:
            logger.error("Model has not been trained yet. Cannot save.")
            return

        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)

            # Save Keras model
            self.model.save(path + "_keras")

            # Save scalers and parameters
            np.savez(
                path,
                feature_scaler_min=self.feature_scaler.min_,
                feature_scaler_scale=self.feature_scaler.scale_,
                target_scaler_min=self.target_scaler.min_,
                target_scaler_scale=self.target_scaler.scale_,
                sequence_length=self.sequence_length,
                units=self.units,
                dropout=self.dropout,
                target_column=self.target_column
            )

            logger.info(f"LSTM model saved to {path}")
        except Exception as e:
            logger.error(f"Error saving LSTM model: {str(e)}")

    def load(self, path: str) -> None:
        """
        Load the LSTM model from disk.

        Args:
            path (str): The path to load the model from
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow not available. Cannot load LSTM model.")
            return

        try:
            # Load Keras model
            self.model = tf.keras.models.load_model(path + "_keras")

            # Load scalers and parameters
            data = np.load(path + ".npz")

            # Restore scalers
            self.feature_scaler = MinMaxScaler()
            self.feature_scaler.min_ = data['feature_scaler_min']
            self.feature_scaler.scale_ = data['feature_scaler_scale']

            self.target_scaler = MinMaxScaler()
            self.target_scaler.min_ = data['target_scaler_min']
            self.target_scaler.scale_ = data['target_scaler_scale']

            # Restore parameters
            self.sequence_length = int(data['sequence_length'])
            self.units = int(data['units'])
            self.dropout = float(data['dropout'])
            self.target_column = str(data['target_column'])

            logger.info(f"LSTM model loaded from {path}")
        except Exception as e:
            logger.error(f"Error loading LSTM model: {str(e)}")
