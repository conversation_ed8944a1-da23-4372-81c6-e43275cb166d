2025-05-25 00:58:56,292 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 00:58:57,452 - app - INFO - Memory management utilities loaded
2025-05-25 00:58:57,452 - app - INFO - Error handling utilities loaded
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy fix
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 00:59:00,875 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 00:59:00,875 - app - INFO - Applied TensorFlow fix
2025-05-25 00:59:00,878 - app.config - INFO - Configuration initialized
2025-05-25 00:59:00,881 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 00:59:00,888 - models.train - INFO - TensorFlow test successful
2025-05-25 00:59:01,298 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 00:59:01,299 - models.train - INFO - Transformer model is available
2025-05-25 00:59:01,299 - models.train - INFO - Using TensorFlow-based models
2025-05-25 00:59:01,300 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 00:59:01,301 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 00:59:01,302 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:59:01,636 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 00:59:01,639 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:01,920 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 00:59:02,407 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 00:59:02,436 - app.utils.session_state - INFO - Initializing session state
2025-05-25 00:59:02,438 - app.utils.session_state - INFO - Session state initialized
2025-05-25 00:59:03,411 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 00:59:03,418 - app.utils.memory_management - INFO - Memory before cleanup: 429.14 MB
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Memory after cleanup: 429.14 MB (freed -0.00 MB)
2025-05-25 00:59:15,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:15,598 - app.utils.memory_management - INFO - Memory before cleanup: 432.43 MB
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Memory after cleanup: 432.43 MB (freed 0.00 MB)
2025-05-25 00:59:16,669 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:16,701 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 00:59:16,701 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,702 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,702 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,721 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 00:59:16,721 - app - INFO - Features shape: (572, 36)
2025-05-25 00:59:16,738 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:16,739 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,739 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,739 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,759 - app.utils.memory_management - INFO - Memory before cleanup: 437.18 MB
2025-05-25 00:59:16,879 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-05-25 00:59:16,881 - app.utils.memory_management - INFO - Memory after cleanup: 437.21 MB (freed -0.04 MB)
2025-05-25 00:59:23,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:23,604 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:23,605 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:23,605 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 00:59:23,606 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 00:59:24,353 - app.utils.memory_management - INFO - Memory before cleanup: 442.44 MB
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Garbage collection: collected 1059 objects
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed 0.00 MB)
2025-05-25 01:03:14,045 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:14,141 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.06 seconds
2025-05-25 01:03:14,145 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:14,145 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:14,146 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:14,401 - app.utils.memory_management - INFO - Memory before cleanup: 445.73 MB
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Garbage collection: collected 1087 objects
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Memory after cleanup: 445.73 MB (freed 0.00 MB)
2025-05-25 01:03:21,135 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:21,160 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:21,160 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:21,160 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:21,160 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:21,404 - app.utils.memory_management - INFO - Memory before cleanup: 446.55 MB
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Garbage collection: collected 924 objects
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Memory after cleanup: 446.55 MB (freed 0.00 MB)
2025-05-25 01:03:42,488 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:42,524 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:42,525 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:42,525 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:42,526 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:42,804 - app.utils.memory_management - INFO - Memory before cleanup: 446.64 MB
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Garbage collection: collected 595 objects
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Memory after cleanup: 446.64 MB (freed 0.00 MB)
2025-05-25 01:05:07,325 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:05:07,334 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:05:07,368 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:05:07,369 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:05:07,370 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:05:07,370 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:05:07,654 - app.utils.memory_management - INFO - Memory before cleanup: 448.05 MB
2025-05-25 01:05:07,775 - app.utils.memory_management - INFO - Garbage collection: collected 1632 objects
2025-05-25 01:05:07,786 - app.utils.memory_management - INFO - Memory after cleanup: 448.05 MB (freed 0.00 MB)
2025-05-25 01:06:05,259 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:05,310 - app.utils.demo_data_generator - INFO - Generated 91 days of demo data for COMI
2025-05-25 01:06:05,580 - app.utils.memory_management - INFO - Memory before cleanup: 448.18 MB
2025-05-25 01:06:05,699 - app.utils.memory_management - INFO - Garbage collection: collected 1188 objects
2025-05-25 01:06:05,700 - app.utils.memory_management - INFO - Memory after cleanup: 448.18 MB (freed 0.00 MB)
2025-05-25 01:06:24,129 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:24,154 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:06:24,155 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:06:24,155 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:06:24,155 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:06:24,457 - app.utils.memory_management - INFO - Memory before cleanup: 448.20 MB
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Garbage collection: collected 1560 objects
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Memory after cleanup: 448.20 MB (freed 0.00 MB)
2025-05-25 01:08:23,275 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:23,321 - app.utils.memory_management - INFO - Memory before cleanup: 448.13 MB
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Garbage collection: collected 263 objects
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Memory after cleanup: 448.13 MB (freed 0.00 MB)
2025-05-25 01:08:26,534 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:26,556 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:08:26,557 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:08:26,557 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:08:26,558 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:08:26,750 - app.utils.memory_management - INFO - Memory before cleanup: 448.19 MB
2025-05-25 01:08:26,900 - app.utils.memory_management - INFO - Garbage collection: collected 1073 objects
2025-05-25 01:08:26,909 - app.utils.memory_management - INFO - Memory after cleanup: 448.19 MB (freed 0.00 MB)
2025-05-25 01:08:42,412 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:42,439 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:42,556 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-25 01:08:42,557 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:47,053 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:47,079 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:56,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:56,507 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 01:08:57,701 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 01:08:57,797 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:08:57,802 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:08:57,804 - app.utils.error_handling - INFO - live_trading_component executed in 1.30 seconds
2025-05-25 01:08:57,807 - app.utils.memory_management - INFO - Memory before cleanup: 450.18 MB
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Memory after cleanup: 450.18 MB (freed 0.00 MB)
2025-05-25 01:09:04,227 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:09:04,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:09:04,251 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-25 01:09:04,252 - app.utils.memory_management - INFO - Memory before cleanup: 450.70 MB
2025-05-25 01:09:04,366 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-25 01:09:04,369 - app.utils.memory_management - INFO - Memory after cleanup: 450.70 MB (freed 0.00 MB)
2025-05-25 01:09:05,613 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:05,636 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:05,637 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:05,637 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:05,637 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:05,832 - app.utils.memory_management - INFO - Memory before cleanup: 452.22 MB
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Garbage collection: collected 1035 objects
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Memory after cleanup: 452.22 MB (freed 0.00 MB)
2025-05-25 01:09:39,004 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:39,048 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:39,048 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:39,049 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:39,049 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:39,329 - app.utils.memory_management - INFO - Memory before cleanup: 452.28 MB
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Garbage collection: collected 948 objects
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Memory after cleanup: 452.28 MB (freed 0.00 MB)
2025-05-25 01:09:41,052 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:41,075 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:41,076 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:41,076 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:41,077 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:41,326 - app.utils.memory_management - INFO - Memory before cleanup: 452.30 MB
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Garbage collection: collected 1670 objects
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Memory after cleanup: 452.30 MB (freed 0.00 MB)
2025-05-25 01:09:42,323 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:42,350 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:42,351 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:42,352 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:42,352 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:42,599 - app.utils.memory_management - INFO - Memory before cleanup: 452.31 MB
2025-05-25 01:09:42,709 - app.utils.memory_management - INFO - Garbage collection: collected 1863 objects
2025-05-25 01:09:42,710 - app.utils.memory_management - INFO - Memory after cleanup: 452.31 MB (freed 0.00 MB)
2025-05-25 01:09:44,076 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:44,100 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:44,100 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:44,101 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:44,101 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:44,345 - app.utils.memory_management - INFO - Memory before cleanup: 452.84 MB
2025-05-25 01:09:44,458 - app.utils.memory_management - INFO - Garbage collection: collected 1880 objects
2025-05-25 01:09:44,459 - app.utils.memory_management - INFO - Memory after cleanup: 452.84 MB (freed 0.00 MB)
2025-05-25 01:10:42,761 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:10:42,769 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:10:42,815 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:10:42,815 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:10:42,815 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:10:42,816 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:10:43,122 - app.utils.memory_management - INFO - Memory before cleanup: 452.85 MB
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Garbage collection: collected 1868 objects
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Memory after cleanup: 452.85 MB (freed 0.00 MB)
2025-05-25 01:17:34,876 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 01:17:34,876 - app - INFO - Memory management utilities loaded
2025-05-25 01:17:34,893 - app - INFO - Error handling utilities loaded
2025-05-25 01:17:34,893 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 01:17:34,895 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 01:17:34,954 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 01:17:34,986 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 01:19:06,307 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 01:19:07,229 - app - INFO - Memory management utilities loaded
2025-05-25 01:19:07,232 - app - INFO - Error handling utilities loaded
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 01:19:07,235 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 01:19:07,235 - app - INFO - Applied NumPy fix
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 01:19:07,235 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 01:19:09,863 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 01:19:09,863 - app - INFO - Applied TensorFlow fix
2025-05-25 01:19:09,866 - app.config - INFO - Configuration initialized
2025-05-25 01:19:09,869 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 01:19:09,876 - models.train - INFO - TensorFlow test successful
2025-05-25 01:19:10,210 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 01:19:10,211 - models.train - INFO - Transformer model is available
2025-05-25 01:19:10,211 - models.train - INFO - Using TensorFlow-based models
2025-05-25 01:19:10,212 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 01:19:10,212 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 01:19:10,213 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 01:19:10,434 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 01:19:10,434 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 01:19:10,492 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 01:19:10,493 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:10,740 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 01:19:11,126 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 01:19:11,141 - app.utils.session_state - INFO - Initializing session state
2025-05-25 01:19:11,142 - app.utils.session_state - INFO - Session state initialized
2025-05-25 01:19:12,373 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:19:12,381 - app.utils.memory_management - INFO - Memory before cleanup: 428.32 MB
2025-05-25 01:19:12,499 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 01:19:12,501 - app.utils.memory_management - INFO - Memory after cleanup: 428.32 MB (freed -0.01 MB)
2025-05-25 01:19:18,606 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:18,639 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 01:19:18,641 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:18,641 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:18,643 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:19,564 - app.utils.memory_management - INFO - Memory before cleanup: 453.61 MB
2025-05-25 01:19:19,675 - app.utils.memory_management - INFO - Garbage collection: collected 2370 objects
2025-05-25 01:19:19,676 - app.utils.memory_management - INFO - Memory after cleanup: 453.66 MB (freed -0.06 MB)
2025-05-25 01:19:26,578 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:26,609 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:26,609 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:26,609 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:26,610 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:26,859 - app.utils.memory_management - INFO - Memory before cleanup: 457.00 MB
2025-05-25 01:19:26,992 - app.utils.memory_management - INFO - Garbage collection: collected 1596 objects
2025-05-25 01:19:26,992 - app.utils.memory_management - INFO - Memory after cleanup: 457.04 MB (freed -0.04 MB)
2025-05-25 01:19:27,718 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:27,747 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:27,750 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:27,750 - app - INFO - Data shape: (572, 36)
2025-05-25 01:19:27,750 - app - INFO - File COMI contains 2025 data
2025-05-25 01:19:27,771 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 01:19:27,771 - app - INFO - Features shape: (572, 36)
2025-05-25 01:19:27,785 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:27,786 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:27,786 - app - INFO - Data shape: (572, 36)
2025-05-25 01:19:27,786 - app - INFO - File COMI contains 2025 data
2025-05-25 01:19:27,805 - app.utils.memory_management - INFO - Memory before cleanup: 459.75 MB
2025-05-25 01:19:27,925 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-05-25 01:19:27,925 - app.utils.memory_management - INFO - Memory after cleanup: 459.75 MB (freed 0.00 MB)
2025-05-25 01:19:51,125 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:51,160 - app.utils.memory_management - INFO - Memory before cleanup: 458.09 MB
2025-05-25 01:19:51,296 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-25 01:19:51,296 - app.utils.memory_management - INFO - Memory after cleanup: 458.09 MB (freed 0.00 MB)
2025-05-25 01:19:53,582 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:53,599 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 01:19:54,768 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 01:19:54,804 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:19:54,804 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:19:54,805 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:19:54,805 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:19:54,810 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:19:54,813 - app.utils.error_handling - INFO - live_trading_component executed in 1.22 seconds
2025-05-25 01:19:54,814 - app.utils.memory_management - INFO - Memory before cleanup: 460.12 MB
2025-05-25 01:19:54,955 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-25 01:19:54,955 - app.utils.memory_management - INFO - Memory after cleanup: 460.12 MB (freed 0.00 MB)
2025-05-25 01:19:58,447 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:58,546 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:19:58,550 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:19:58,551 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-25 01:19:58,552 - app.utils.memory_management - INFO - Memory before cleanup: 461.04 MB
2025-05-25 01:19:58,680 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-25 01:19:58,681 - app.utils.memory_management - INFO - Memory after cleanup: 461.04 MB (freed 0.00 MB)
2025-05-25 01:19:59,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:59,438 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:59,438 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:59,439 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:59,439 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:59,698 - app.utils.memory_management - INFO - Memory before cleanup: 462.61 MB
2025-05-25 01:19:59,835 - app.utils.memory_management - INFO - Garbage collection: collected 1653 objects
2025-05-25 01:19:59,836 - app.utils.memory_management - INFO - Memory after cleanup: 462.61 MB (freed 0.00 MB)
2025-05-25 01:20:16,923 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:16,956 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:16,957 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:16,957 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:16,958 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:17,217 - app.utils.memory_management - INFO - Memory before cleanup: 463.61 MB
2025-05-25 01:20:17,381 - app.utils.memory_management - INFO - Garbage collection: collected 1680 objects
2025-05-25 01:20:17,385 - app.utils.memory_management - INFO - Memory after cleanup: 463.61 MB (freed 0.00 MB)
2025-05-25 01:20:18,711 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:18,742 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:18,743 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:18,743 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:18,743 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:19,079 - app.utils.memory_management - INFO - Memory before cleanup: 463.62 MB
2025-05-25 01:20:19,206 - app.utils.memory_management - INFO - Garbage collection: collected 2576 objects
2025-05-25 01:20:19,207 - app.utils.memory_management - INFO - Memory after cleanup: 463.62 MB (freed 0.00 MB)
2025-05-25 01:20:24,450 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:24,527 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:24,535 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:24,535 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:24,536 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:24,852 - app.utils.memory_management - INFO - Memory before cleanup: 464.10 MB
2025-05-25 01:20:24,964 - app.utils.memory_management - INFO - Garbage collection: collected 2476 objects
2025-05-25 01:20:24,965 - app.utils.memory_management - INFO - Memory after cleanup: 464.10 MB (freed 0.00 MB)
2025-05-25 01:21:34,912 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:21:34,950 - app.utils.common - INFO - Loaded stock data for SWDY from data/stocks\SWDY.csv in 0.00 seconds
2025-05-25 01:21:34,951 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-08
2025-05-25 01:21:34,951 - app.utils.common - INFO - Data shape: (307, 6)
2025-05-25 01:21:34,952 - app.utils.common - INFO - File SWDY contains 2025 data
2025-05-25 01:21:35,320 - app.utils.memory_management - INFO - Memory before cleanup: 464.08 MB
2025-05-25 01:21:35,438 - app.utils.memory_management - INFO - Garbage collection: collected 2536 objects
2025-05-25 01:21:35,439 - app.utils.memory_management - INFO - Memory after cleanup: 464.08 MB (freed 0.00 MB)
2025-05-25 01:21:40,556 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:21:40,582 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:21:40,583 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:21:40,583 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:21:40,584 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:21:40,886 - app.utils.memory_management - INFO - Memory before cleanup: 464.07 MB
2025-05-25 01:21:41,034 - app.utils.memory_management - INFO - Garbage collection: collected 2585 objects
2025-05-25 01:21:41,035 - app.utils.memory_management - INFO - Memory after cleanup: 464.07 MB (freed 0.00 MB)
2025-05-25 02:07:10,967 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:07:10,983 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 02:07:11,021 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:07:11,022 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:07:11,022 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:07:11,023 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:07:11,414 - app.utils.memory_management - INFO - Memory before cleanup: 459.91 MB
2025-05-25 02:07:11,541 - app.utils.memory_management - INFO - Garbage collection: collected 2620 objects
2025-05-25 02:07:11,542 - app.utils.memory_management - INFO - Memory after cleanup: 459.88 MB (freed 0.03 MB)
2025-05-25 02:12:50,211 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:12:50,215 - app - INFO - Memory management utilities loaded
2025-05-25 02:12:50,216 - app - INFO - Error handling utilities loaded
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:13:13,839 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:13:14,895 - app - INFO - Memory management utilities loaded
2025-05-25 02:13:14,898 - app - INFO - Error handling utilities loaded
2025-05-25 02:13:14,899 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:13:14,901 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:13:14,901 - app - INFO - Applied NumPy fix
2025-05-25 02:13:14,902 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:13:14,902 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:13:14,903 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:13:17,853 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:13:17,853 - app - INFO - Applied TensorFlow fix
2025-05-25 02:13:17,854 - app.config - INFO - Configuration initialized
2025-05-25 02:13:17,857 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:13:17,864 - models.train - INFO - TensorFlow test successful
2025-05-25 02:13:18,231 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:13:18,232 - models.train - INFO - Transformer model is available
2025-05-25 02:13:18,232 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:13:18,234 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:13:18,234 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:13:18,235 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:13:18,459 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:13:18,459 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:13:18,459 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:13:18,459 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:13:18,518 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:13:18,518 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:18,788 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:13:19,215 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 02:13:19,235 - app.utils.session_state - INFO - Initializing session state
2025-05-25 02:13:19,237 - app.utils.session_state - INFO - Session state initialized
2025-05-25 02:13:20,167 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 02:13:20,177 - app.utils.memory_management - INFO - Memory before cleanup: 428.54 MB
2025-05-25 02:13:20,289 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 02:13:20,290 - app.utils.memory_management - INFO - Memory after cleanup: 428.55 MB (freed -0.00 MB)
2025-05-25 02:13:26,844 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:26,876 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:13:26,877 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:26,877 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:26,877 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:27,780 - app.utils.memory_management - INFO - Memory before cleanup: 453.90 MB
2025-05-25 02:13:27,887 - app.utils.memory_management - INFO - Garbage collection: collected 2388 objects
2025-05-25 02:13:27,887 - app.utils.memory_management - INFO - Memory after cleanup: 453.90 MB (freed 0.00 MB)
2025-05-25 02:13:37,447 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:37,475 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:37,476 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:37,476 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:37,476 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:37,720 - app.utils.memory_management - INFO - Memory before cleanup: 457.46 MB
2025-05-25 02:13:37,838 - app.utils.memory_management - INFO - Garbage collection: collected 1596 objects
2025-05-25 02:13:37,839 - app.utils.memory_management - INFO - Memory after cleanup: 457.50 MB (freed -0.04 MB)
2025-05-25 02:13:39,272 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:39,300 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:39,300 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:39,301 - app - INFO - Data shape: (572, 36)
2025-05-25 02:13:39,301 - app - INFO - File COMI contains 2025 data
2025-05-25 02:13:39,321 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 02:13:39,322 - app - INFO - Features shape: (572, 36)
2025-05-25 02:13:39,334 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:39,334 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:39,335 - app - INFO - Data shape: (572, 36)
2025-05-25 02:13:39,335 - app - INFO - File COMI contains 2025 data
2025-05-25 02:13:39,356 - app.utils.memory_management - INFO - Memory before cleanup: 459.63 MB
2025-05-25 02:13:39,476 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-05-25 02:13:39,477 - app.utils.memory_management - INFO - Memory after cleanup: 459.63 MB (freed 0.00 MB)
2025-05-25 02:13:44,748 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:44,761 - app.utils.memory_management - INFO - Memory before cleanup: 457.83 MB
2025-05-25 02:13:44,877 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-25 02:13:44,877 - app.utils.memory_management - INFO - Memory after cleanup: 457.83 MB (freed 0.00 MB)
2025-05-25 02:13:46,812 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:46,844 - app.utils.memory_management - INFO - Memory before cleanup: 457.86 MB
2025-05-25 02:13:46,954 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-25 02:13:46,954 - app.utils.memory_management - INFO - Memory after cleanup: 457.86 MB (freed 0.00 MB)
2025-05-25 02:13:49,289 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:49,315 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:49,316 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:49,316 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:49,317 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:49,548 - app.utils.memory_management - INFO - Memory before cleanup: 458.87 MB
2025-05-25 02:13:49,677 - app.utils.memory_management - INFO - Garbage collection: collected 1642 objects
2025-05-25 02:13:49,678 - app.utils.memory_management - INFO - Memory after cleanup: 458.87 MB (freed 0.00 MB)
2025-05-25 02:14:07,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:07,759 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:07,759 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:07,760 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:07,760 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:08,004 - app.utils.memory_management - INFO - Memory before cleanup: 458.99 MB
2025-05-25 02:14:08,120 - app.utils.memory_management - INFO - Garbage collection: collected 1643 objects
2025-05-25 02:14:08,120 - app.utils.memory_management - INFO - Memory after cleanup: 458.99 MB (freed 0.00 MB)
2025-05-25 02:14:09,074 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:09,097 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:09,098 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:09,098 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:09,098 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:09,407 - app.utils.memory_management - INFO - Memory before cleanup: 459.01 MB
2025-05-25 02:14:09,523 - app.utils.memory_management - INFO - Garbage collection: collected 2601 objects
2025-05-25 02:14:09,523 - app.utils.memory_management - INFO - Memory after cleanup: 459.01 MB (freed 0.00 MB)
2025-05-25 02:14:10,636 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:10,670 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:10,671 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:10,671 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:10,671 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:10,978 - app.utils.memory_management - INFO - Memory before cleanup: 459.71 MB
2025-05-25 02:14:11,092 - app.utils.memory_management - INFO - Garbage collection: collected 3372 objects
2025-05-25 02:14:11,093 - app.utils.memory_management - INFO - Memory after cleanup: 459.71 MB (freed 0.00 MB)
2025-05-25 02:14:15,254 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:15,282 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:15,283 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:15,283 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:15,284 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:15,610 - app.utils.memory_management - INFO - Memory before cleanup: 459.72 MB
2025-05-25 02:14:15,737 - app.utils.memory_management - INFO - Garbage collection: collected 2649 objects
2025-05-25 02:14:15,738 - app.utils.memory_management - INFO - Memory after cleanup: 459.72 MB (freed 0.00 MB)
2025-05-25 02:16:11,498 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:11,545 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:11,546 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:11,546 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:11,546 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:11,966 - app.utils.memory_management - INFO - Memory before cleanup: 459.69 MB
2025-05-25 02:16:12,071 - app.utils.memory_management - INFO - Garbage collection: collected 2163 objects
2025-05-25 02:16:12,071 - app.utils.memory_management - INFO - Memory after cleanup: 459.65 MB (freed 0.04 MB)
2025-05-25 02:16:13,672 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:13,697 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:13,698 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:13,698 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:13,698 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:14,010 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:14,154 - app.utils.memory_management - INFO - Garbage collection: collected 2165 objects
2025-05-25 02:16:14,154 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:18,117 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:18,142 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:18,143 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:18,143 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:18,143 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:18,458 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:18,558 - app.utils.memory_management - INFO - Garbage collection: collected 2135 objects
2025-05-25 02:16:18,558 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:19,823 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:19,851 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:16:19,852 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:19,852 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:19,852 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:20,160 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:20,282 - app.utils.memory_management - INFO - Garbage collection: collected 2167 objects
2025-05-25 02:16:20,282 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:21,898 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:21,931 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:16:21,932 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:21,932 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:21,933 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:22,255 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:22,362 - app.utils.memory_management - INFO - Garbage collection: collected 2167 objects
2025-05-25 02:16:22,362 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:22,846 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:22,872 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:22,873 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:22,873 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:22,873 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:23,199 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:23,330 - app.utils.memory_management - INFO - Garbage collection: collected 2139 objects
2025-05-25 02:16:23,330 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:48:26,986 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:48:27,899 - app - INFO - Memory management utilities loaded
2025-05-25 02:48:27,900 - app - INFO - Error handling utilities loaded
2025-05-25 02:48:27,901 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:48:27,901 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:48:27,902 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:48:27,903 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:48:27,903 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:48:27,904 - app - INFO - Applied NumPy fix
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:48:27,904 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:48:30,507 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:48:30,507 - app - INFO - Applied TensorFlow fix
2025-05-25 02:48:30,507 - app.config - INFO - Configuration initialized
2025-05-25 02:48:30,507 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:48:30,528 - models.train - INFO - TensorFlow test successful
2025-05-25 02:48:30,851 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:48:30,851 - models.train - INFO - Transformer model is available
2025-05-25 02:48:30,851 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:48:30,852 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:48:30,853 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:48:30,854 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:48:31,074 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:48:31,074 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:48:31,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:48:31,074 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:48:31,076 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:48:31,136 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:48:31,138 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:48:31,392 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:48:31,983 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 02:51:02,019 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:51:02,920 - app - INFO - Memory management utilities loaded
2025-05-25 02:51:02,920 - app - INFO - Error handling utilities loaded
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:51:02,920 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:51:02,920 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:51:02,920 - app - INFO - Applied NumPy fix
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:51:02,920 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:51:02,920 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:51:05,497 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:51:05,497 - app - INFO - Applied TensorFlow fix
2025-05-25 02:51:05,499 - app.config - INFO - Configuration initialized
2025-05-25 02:51:05,501 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:51:05,508 - models.train - INFO - TensorFlow test successful
2025-05-25 02:51:05,842 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:51:05,842 - models.train - INFO - Transformer model is available
2025-05-25 02:51:05,842 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:51:05,843 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:51:05,844 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:51:05,845 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:51:06,068 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:51:06,068 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:51:06,068 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:51:06,068 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:51:06,068 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:51:06,069 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:51:06,069 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:51:06,069 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:51:06,069 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:51:06,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:51:06,175 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:51:06,176 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:51:06,473 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:51:06,991 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 02:52:10,869 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:52:11,854 - app - INFO - Memory management utilities loaded
2025-05-25 02:52:11,854 - app - INFO - Error handling utilities loaded
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:52:11,854 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:52:11,854 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:52:11,854 - app - INFO - Applied NumPy fix
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:52:11,854 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:52:11,854 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:52:14,602 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:52:14,603 - app - INFO - Applied TensorFlow fix
2025-05-25 02:52:14,604 - app.config - INFO - Configuration initialized
2025-05-25 02:52:14,607 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:52:14,614 - models.train - INFO - TensorFlow test successful
2025-05-25 02:52:15,043 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:52:15,044 - models.train - INFO - Transformer model is available
2025-05-25 02:52:15,044 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:52:15,045 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:52:15,045 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:52:15,047 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:52:15,256 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:52:15,256 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:52:15,256 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:52:15,310 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:52:15,311 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:52:15,543 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:52:15,894 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 02:52:15,909 - app.utils.session_state - INFO - Initializing session state
2025-05-25 02:52:15,910 - app.utils.session_state - INFO - Session state initialized
2025-05-25 02:52:16,859 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 02:52:16,868 - app.utils.memory_management - INFO - Memory before cleanup: 428.38 MB
2025-05-25 02:52:16,976 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 02:52:16,977 - app.utils.memory_management - INFO - Memory after cleanup: 428.38 MB (freed -0.00 MB)
2025-05-25 02:52:43,135 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:52:43,177 - app - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-25 02:52:43,178 - app - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 02:52:43,178 - app - INFO - Data shape: (309, 6)
2025-05-25 02:52:43,178 - app - INFO - File ABUK contains 2025 data
2025-05-25 02:52:43,193 - app.utils.memory_management - INFO - Memory before cleanup: 432.30 MB
2025-05-25 02:52:43,332 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 02:52:43,334 - app.utils.memory_management - INFO - Memory after cleanup: 432.30 MB (freed -0.00 MB)
2025-05-25 02:52:54,421 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:52:54,428 - app.utils.session_state - INFO - Initializing session state
2025-05-25 02:52:54,429 - app.utils.session_state - INFO - Session state initialized
2025-05-25 02:52:54,454 - app.utils.memory_management - INFO - Memory before cleanup: 433.32 MB
2025-05-25 02:52:54,683 - app.utils.memory_management - INFO - Garbage collection: collected 192 objects
2025-05-25 02:52:54,683 - app.utils.memory_management - INFO - Memory after cleanup: 433.36 MB (freed -0.04 MB)
2025-05-25 02:53:08,439 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:08,454 - app.utils.memory_management - INFO - Memory before cleanup: 433.30 MB
2025-05-25 02:53:08,584 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-25 02:53:08,584 - app.utils.memory_management - INFO - Memory after cleanup: 433.30 MB (freed 0.00 MB)
2025-05-25 02:53:09,581 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:09,604 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:53:09,605 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:53:09,605 - app - INFO - Data shape: (572, 36)
2025-05-25 02:53:09,605 - app - INFO - File COMI contains 2025 data
2025-05-25 02:53:09,628 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 02:53:09,629 - app - INFO - Features shape: (572, 36)
2025-05-25 02:53:09,642 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:53:09,643 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:53:09,643 - app - INFO - Data shape: (572, 36)
2025-05-25 02:53:09,643 - app - INFO - File COMI contains 2025 data
2025-05-25 02:53:09,671 - app.utils.memory_management - INFO - Memory before cleanup: 435.74 MB
2025-05-25 02:53:09,812 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-05-25 02:53:09,813 - app.utils.memory_management - INFO - Memory after cleanup: 435.74 MB (freed 0.00 MB)
2025-05-25 02:53:15,914 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:15,949 - app.utils.memory_management - INFO - Memory before cleanup: 435.68 MB
2025-05-25 02:53:16,270 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-05-25 02:53:16,270 - app.utils.memory_management - INFO - Memory after cleanup: 435.68 MB (freed 0.00 MB)
2025-05-25 02:53:23,114 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:23,129 - app.utils.memory_management - INFO - Memory before cleanup: 435.69 MB
2025-05-25 02:53:23,270 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-25 02:53:23,272 - app.utils.memory_management - INFO - Memory after cleanup: 435.69 MB (freed 0.00 MB)
2025-05-25 02:53:24,526 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:24,540 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 02:53:25,609 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 02:53:25,647 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:53:25,647 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 02:53:25,647 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:53:25,648 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 02:53:25,651 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 02:53:25,653 - app.utils.error_handling - INFO - live_trading_component executed in 1.12 seconds
2025-05-25 02:53:25,654 - app.utils.memory_management - INFO - Memory before cleanup: 437.81 MB
2025-05-25 02:53:25,780 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-25 02:53:25,781 - app.utils.memory_management - INFO - Memory after cleanup: 437.81 MB (freed 0.00 MB)
2025-05-25 02:53:53,505 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:53,524 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:53:53,524 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 02:53:53,525 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:53:53,525 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 02:53:53,528 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 02:53:53,530 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-25 02:53:53,531 - app.utils.memory_management - INFO - Memory before cleanup: 438.99 MB
2025-05-25 02:53:53,634 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-05-25 02:53:53,634 - app.utils.memory_management - INFO - Memory after cleanup: 438.99 MB (freed 0.00 MB)
2025-05-25 02:53:54,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:53:54,590 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:53:54,591 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:53:54,591 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:53:54,599 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,599 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:53:54,599 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-25 02:53:54,612 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-25 02:53:54,618 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 02:53:54,618 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 02:53:54,618 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,618 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 02:53:54,618 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 02:53:54,618 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 02:53:54,618 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 02:53:54,634 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 02:53:54,635 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 02:53:54,637 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 02:53:54,637 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 02:53:54,637 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 02:53:54,638 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 02:53:54,638 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 02:53:54,638 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 02:53:54,638 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 02:53:54,638 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 02:53:54,639 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 02:53:54,640 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 02:53:54,640 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 02:53:54,640 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 02:53:54,641 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-25 02:53:54,750 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:53:54,769 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 02:53:54,785 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 02:53:54,786 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 02:53:54,794 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,798 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-25 02:53:54,798 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-25 02:53:54,798 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-25 02:53:54,799 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-25 02:53:54,810 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,811 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-25 02:53:54,811 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-25 02:53:54,811 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-25 02:53:54,812 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-25 02:53:54,815 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,815 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-25 02:53:54,815 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:53:54,818 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:53:54,818 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:53:54,822 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:53:54,822 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:53:54,830 - app.utils.memory_management - INFO - Memory before cleanup: 439.17 MB
2025-05-25 02:53:54,935 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-25 02:53:54,935 - app.utils.memory_management - INFO - Memory after cleanup: 439.17 MB (freed 0.00 MB)
2025-05-25 02:54:05,400 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:54:05,410 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:54:05,411 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:54:05,411 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:54:05,417 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,418 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:54:05,418 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-25 02:54:05,429 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-25 02:54:05,438 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 02:54:05,439 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 02:54:05,443 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 02:54:05,446 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 02:54:05,446 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-25 02:54:05,557 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-25 02:54:05,579 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:54:05,581 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:54:05,582 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:54:05,588 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,589 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:54:05,590 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 02:54:05,594 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 02:54:05,595 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 02:54:05,602 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,606 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-25 02:54:05,607 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-25 02:54:05,607 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-25 02:54:05,607 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-25 02:54:05,612 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,612 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-25 02:54:05,612 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-25 02:54:05,612 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-25 02:54:05,613 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-25 02:54:05,617 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,618 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-25 02:54:05,618 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 02:54:05,618 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 02:54:05,619 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 02:54:05,622 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 02:54:05,623 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 02:54:05,631 - app.utils.memory_management - INFO - Memory before cleanup: 439.56 MB
2025-05-25 02:54:05,735 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-25 02:54:05,737 - app.utils.memory_management - INFO - Memory after cleanup: 439.56 MB (freed 0.00 MB)
2025-05-25 02:54:06,285 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:54:06,299 - app.utils.memory_management - INFO - Memory before cleanup: 439.56 MB
2025-05-25 02:54:06,406 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-25 02:54:06,407 - app.utils.memory_management - INFO - Memory after cleanup: 439.56 MB (freed 0.00 MB)
2025-05-25 03:01:25,854 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 03:01:26,770 - app - INFO - Memory management utilities loaded
2025-05-25 03:01:26,771 - app - INFO - Error handling utilities loaded
2025-05-25 03:01:26,773 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 03:01:26,774 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 03:01:26,774 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 03:01:26,774 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 03:01:26,775 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 03:01:26,775 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 03:01:26,775 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 03:01:26,775 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 03:01:26,775 - app - INFO - Applied NumPy fix
2025-05-25 03:01:26,776 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 03:01:26,776 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 03:01:26,776 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 03:01:26,777 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 03:01:26,777 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 03:01:26,777 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 03:01:26,777 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 03:01:26,777 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 03:01:29,692 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 03:01:29,692 - app - INFO - Applied TensorFlow fix
2025-05-25 03:01:29,694 - app.config - INFO - Configuration initialized
2025-05-25 03:01:29,696 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 03:01:29,702 - models.train - INFO - TensorFlow test successful
2025-05-25 03:01:30,022 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 03:01:30,023 - models.train - INFO - Transformer model is available
2025-05-25 03:01:30,023 - models.train - INFO - Using TensorFlow-based models
2025-05-25 03:01:30,024 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 03:01:30,024 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 03:01:30,026 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 03:01:30,239 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 03:01:30,239 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 03:01:30,239 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 03:01:30,240 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 03:01:30,240 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 03:01:30,240 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 03:01:30,240 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 03:01:30,241 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 03:01:30,241 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 03:01:30,242 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 03:01:30,295 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 03:01:30,297 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:01:30,541 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 03:01:30,896 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 03:01:30,907 - app.utils.session_state - INFO - Initializing session state
2025-05-25 03:01:30,908 - app.utils.session_state - INFO - Session state initialized
2025-05-25 03:01:31,845 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 03:01:31,853 - app.utils.memory_management - INFO - Memory before cleanup: 428.01 MB
2025-05-25 03:01:31,960 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 03:01:31,960 - app.utils.memory_management - INFO - Memory after cleanup: 428.02 MB (freed -0.02 MB)
2025-05-25 03:01:49,329 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:01:49,329 - app.utils.memory_management - INFO - Memory before cleanup: 431.98 MB
2025-05-25 03:01:49,477 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 03:01:49,477 - app.utils.memory_management - INFO - Memory after cleanup: 431.98 MB (freed 0.00 MB)
2025-05-25 03:01:51,133 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:01:51,163 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:01:51,163 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:01:51,163 - app - INFO - Data shape: (572, 36)
2025-05-25 03:01:51,163 - app - INFO - File COMI contains 2025 data
2025-05-25 03:01:51,191 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-25 03:01:51,192 - app - INFO - Features shape: (572, 36)
2025-05-25 03:01:51,207 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:01:51,207 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:01:51,207 - app - INFO - Data shape: (572, 36)
2025-05-25 03:01:51,208 - app - INFO - File COMI contains 2025 data
2025-05-25 03:01:51,209 - app.utils.memory_management - INFO - Memory before cleanup: 436.31 MB
2025-05-25 03:01:51,327 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-25 03:01:51,328 - app.utils.memory_management - INFO - Memory after cleanup: 436.35 MB (freed -0.04 MB)
2025-05-25 03:02:07,222 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:07,562 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-25 03:02:07,990 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:02:08,000 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-25 03:02:08,001 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 03:02:08,001 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-25 03:02:08,002 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-25 03:02:08,519 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.53 seconds
2025-05-25 03:02:09,063 - app.utils.memory_management - INFO - Memory before cleanup: 446.05 MB
2025-05-25 03:02:09,222 - app.utils.memory_management - INFO - Garbage collection: collected 3325 objects
2025-05-25 03:02:09,223 - app.utils.memory_management - INFO - Memory after cleanup: 446.04 MB (freed 0.02 MB)
2025-05-25 03:02:11,027 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:11,034 - app.utils.session_state - INFO - Initializing session state
2025-05-25 03:02:11,035 - app.utils.session_state - INFO - Session state initialized
2025-05-25 03:02:11,046 - app.utils.memory_management - INFO - Memory before cleanup: 447.29 MB
2025-05-25 03:02:11,175 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-05-25 03:02:11,175 - app.utils.memory_management - INFO - Memory after cleanup: 447.29 MB (freed 0.00 MB)
2025-05-25 03:02:17,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:17,511 - app.utils.memory_management - INFO - Memory before cleanup: 447.30 MB
2025-05-25 03:02:17,688 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-25 03:02:17,689 - app.utils.memory_management - INFO - Memory after cleanup: 447.30 MB (freed 0.00 MB)
2025-05-25 03:02:18,656 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:18,682 - app.utils.memory_management - INFO - Memory before cleanup: 447.33 MB
2025-05-25 03:02:18,824 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-25 03:02:18,824 - app.utils.memory_management - INFO - Memory after cleanup: 447.33 MB (freed 0.00 MB)
2025-05-25 03:02:22,144 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:22,393 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-25 03:02:22,596 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:02:22,596 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-25 03:02:22,596 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 03:02:22,609 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-25 03:02:22,610 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-25 03:02:22,825 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.23 seconds
2025-05-25 03:02:23,053 - app.utils.memory_management - INFO - Memory before cleanup: 448.64 MB
2025-05-25 03:02:23,237 - app.utils.memory_management - INFO - Garbage collection: collected 3341 objects
2025-05-25 03:02:23,247 - app.utils.memory_management - INFO - Memory after cleanup: 448.59 MB (freed 0.05 MB)
2025-05-25 03:02:35,062 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:35,394 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-25 03:02:35,570 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:02:35,578 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-25 03:02:35,579 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 03:02:35,582 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-25 03:02:35,582 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-25 03:02:35,776 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-25 03:02:36,005 - app.utils.memory_management - INFO - Memory before cleanup: 449.44 MB
2025-05-25 03:02:36,157 - app.utils.memory_management - INFO - Garbage collection: collected 3933 objects
2025-05-25 03:02:36,157 - app.utils.memory_management - INFO - Memory after cleanup: 449.44 MB (freed 0.00 MB)
2025-05-25 03:02:36,341 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:36,356 - app.utils.memory_management - INFO - Memory before cleanup: 449.43 MB
2025-05-25 03:02:36,490 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-25 03:02:36,491 - app.utils.memory_management - INFO - Memory after cleanup: 449.43 MB (freed 0.00 MB)
2025-05-25 03:02:40,661 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:40,677 - app.utils.memory_management - INFO - Memory before cleanup: 449.43 MB
2025-05-25 03:02:40,794 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-25 03:02:40,794 - app.utils.memory_management - INFO - Memory after cleanup: 449.43 MB (freed 0.00 MB)
2025-05-25 03:02:41,714 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:41,725 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-25 03:02:42,284 - app.utils.memory_management - INFO - Memory before cleanup: 460.08 MB
2025-05-25 03:02:42,409 - app.utils.memory_management - INFO - Garbage collection: collected 418 objects
2025-05-25 03:02:42,410 - app.utils.memory_management - INFO - Memory after cleanup: 460.08 MB (freed 0.00 MB)
2025-05-25 03:02:57,218 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:57,479 - app.utils.memory_management - INFO - Memory before cleanup: 463.54 MB
2025-05-25 03:02:57,677 - app.utils.memory_management - INFO - Garbage collection: collected 324 objects
2025-05-25 03:02:57,677 - app.utils.memory_management - INFO - Memory after cleanup: 463.54 MB (freed 0.00 MB)
2025-05-25 03:02:58,096 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:02:58,114 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 03:02:59,208 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 03:02:59,243 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:02:59,243 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 03:02:59,247 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:02:59,249 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 03:02:59,253 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 03:02:59,254 - app.utils.error_handling - INFO - live_trading_component executed in 1.16 seconds
2025-05-25 03:02:59,255 - app.utils.memory_management - INFO - Memory before cleanup: 466.04 MB
2025-05-25 03:02:59,386 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-05-25 03:02:59,387 - app.utils.memory_management - INFO - Memory after cleanup: 466.04 MB (freed 0.00 MB)
2025-05-25 03:03:09,072 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:09,140 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:09,141 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 03:03:09,142 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:09,142 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 03:03:09,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 03:03:09,147 - app.utils.error_handling - INFO - live_trading_component executed in 0.03 seconds
2025-05-25 03:03:09,147 - app.utils.memory_management - INFO - Memory before cleanup: 466.35 MB
2025-05-25 03:03:09,280 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-25 03:03:09,281 - app.utils.memory_management - INFO - Memory after cleanup: 466.35 MB (freed 0.00 MB)
2025-05-25 03:03:10,195 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:10,209 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:10,209 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:10,210 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:10,215 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,215 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:10,216 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-25 03:03:10,226 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-25 03:03:10,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 03:03:10,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 03:03:10,247 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,253 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 03:03:10,253 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 03:03:10,253 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 03:03:10,254 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 03:03:10,254 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 03:03:10,255 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 03:03:10,256 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 03:03:10,257 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 03:03:10,257 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 03:03:10,257 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 03:03:10,258 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 03:03:10,258 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 03:03:10,259 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 03:03:10,260 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 03:03:10,261 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 03:03:10,262 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 03:03:10,263 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 03:03:10,263 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 03:03:10,264 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-25 03:03:10,450 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:10,508 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 03:03:10,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 03:03:10,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 03:03:10,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,539 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-25 03:03:10,541 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-25 03:03:10,542 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-25 03:03:10,543 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-25 03:03:10,548 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,548 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-25 03:03:10,548 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-25 03:03:10,549 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-25 03:03:10,549 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-25 03:03:10,553 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,554 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-25 03:03:10,554 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:10,557 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:10,558 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:10,563 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:10,563 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:10,575 - app.utils.memory_management - INFO - Memory before cleanup: 466.37 MB
2025-05-25 03:03:10,717 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-25 03:03:10,718 - app.utils.memory_management - INFO - Memory after cleanup: 466.37 MB (freed 0.00 MB)
2025-05-25 03:03:16,393 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:16,407 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:16,407 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:16,407 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:16,414 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,416 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:16,416 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-25 03:03:16,431 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-25 03:03:16,438 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 03:03:16,439 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 03:03:16,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,449 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-25 03:03:16,450 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-25 03:03:16,450 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-25 03:03:16,450 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-25 03:03:16,450 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-25 03:03:16,451 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-25 03:03:16,452 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-25 03:03:16,453 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-25 03:03:16,453 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-25 03:03:16,454 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-25 03:03:16,601 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-25 03:03:16,625 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:16,626 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:16,626 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:16,634 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,634 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:16,635 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 03:03:16,639 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-25 03:03:16,639 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-25 03:03:16,649 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,653 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-25 03:03:16,654 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-25 03:03:16,654 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-25 03:03:16,654 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-25 03:03:16,659 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,661 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-25 03:03:16,661 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-25 03:03:16,663 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-25 03:03:16,663 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-25 03:03:16,667 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,668 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-25 03:03:16,668 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 03:03:16,668 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-25 03:03:16,669 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 03:03:16,673 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-25 03:03:16,673 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-25 03:03:16,687 - app.utils.memory_management - INFO - Memory before cleanup: 466.56 MB
2025-05-25 03:03:16,856 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-25 03:03:16,857 - app.utils.memory_management - INFO - Memory after cleanup: 466.56 MB (freed 0.00 MB)
2025-05-25 03:03:17,197 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:17,238 - app.components.performance_metrics - INFO - Target time 2025-05-24 21:46:55.900507 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,255 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-25 03:03:17,268 - app.components.performance_metrics - INFO - Target time 2025-05-18 10:04:34.514509 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,277 - app.components.performance_metrics - INFO - Target time 2025-05-18 10:06:16.657625 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,298 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-25 03:03:17,316 - app.components.performance_metrics - INFO - Target time 2025-05-18 16:56:36.279442 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,331 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-25 03:03:17,342 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:32:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,366 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:42:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,378 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:57:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,391 - app.components.performance_metrics - INFO - Target time 2025-05-18 01:27:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,407 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:28:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,422 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:38:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,435 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:53:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,448 - app.components.performance_metrics - INFO - Target time 2025-05-18 14:23:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,457 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:29:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,468 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:39:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,477 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:54:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,479 - app.components.performance_metrics - INFO - Target time 2025-05-19 12:24:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,498 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:01:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,498 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:16:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,520 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:46:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,530 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:00:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,540 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:10:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,546 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:25:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,546 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:55:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,562 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:21:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,579 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:36:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,579 - app.components.performance_metrics - INFO - Target time 2025-05-19 15:06:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,596 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:35:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,596 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:50:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,612 - app.components.performance_metrics - INFO - Target time 2025-05-19 15:20:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,629 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:37:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,629 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:47:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,646 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:02:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,661 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:32:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,662 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:49:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,679 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:04:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,679 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:34:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:17,864 - app.utils.memory_management - INFO - Memory before cleanup: 469.21 MB
2025-05-25 03:03:17,997 - app.utils.memory_management - INFO - Garbage collection: collected 1624 objects
2025-05-25 03:03:17,997 - app.utils.memory_management - INFO - Memory after cleanup: 469.21 MB (freed 0.00 MB)
2025-05-25 03:03:19,570 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:19,600 - app.components.performance_metrics - INFO - Target time 2025-05-24 21:46:55.900507 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,609 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-25 03:03:19,620 - app.components.performance_metrics - INFO - Target time 2025-05-18 10:04:34.514509 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,633 - app.components.performance_metrics - INFO - Target time 2025-05-18 10:06:16.657625 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,633 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-25 03:03:19,658 - app.components.performance_metrics - INFO - Target time 2025-05-18 16:56:36.279442 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,668 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-25 03:03:19,668 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:32:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,690 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:42:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,699 - app.components.performance_metrics - INFO - Target time 2025-05-18 00:57:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,710 - app.components.performance_metrics - INFO - Target time 2025-05-18 01:27:45.365458 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,715 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:28:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,715 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:38:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,732 - app.components.performance_metrics - INFO - Target time 2025-05-18 13:53:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,732 - app.components.performance_metrics - INFO - Target time 2025-05-18 14:23:46.911438 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,749 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:29:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,764 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:39:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,765 - app.components.performance_metrics - INFO - Target time 2025-05-19 11:54:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,785 - app.components.performance_metrics - INFO - Target time 2025-05-19 12:24:00.561987 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,791 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:01:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,798 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:16:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,798 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:46:14.033939 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,815 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:00:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,833 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:10:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,833 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:25:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,855 - app.components.performance_metrics - INFO - Target time 2025-05-19 13:55:10.559110 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,865 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:21:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,873 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:36:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,882 - app.components.performance_metrics - INFO - Target time 2025-05-19 15:06:56.821923 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,882 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:35:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,898 - app.components.performance_metrics - INFO - Target time 2025-05-19 14:50:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,898 - app.components.performance_metrics - INFO - Target time 2025-05-19 15:20:25.581323 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,914 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:37:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,914 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:47:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,931 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:02:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,948 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:32:50.090856 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,948 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:49:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,965 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:04:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:19,965 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:34:09.144682 is in the future compared to latest data 2025-05-18 00:00:00
2025-05-25 03:03:20,101 - app.utils.memory_management - INFO - Memory before cleanup: 469.83 MB
2025-05-25 03:03:20,224 - app.utils.memory_management - INFO - Garbage collection: collected 1551 objects
2025-05-25 03:03:20,224 - app.utils.memory_management - INFO - Memory after cleanup: 469.83 MB (freed 0.00 MB)
2025-05-25 03:03:20,477 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:20,504 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:03:20,505 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:03:20,505 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:03:20,507 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:03:21,301 - app.utils.memory_management - INFO - Memory before cleanup: 479.49 MB
2025-05-25 03:03:21,435 - app.utils.memory_management - INFO - Garbage collection: collected 2300 objects
2025-05-25 03:03:21,435 - app.utils.memory_management - INFO - Memory after cleanup: 479.75 MB (freed -0.25 MB)
2025-05-25 03:03:23,261 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:23,289 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:03:23,290 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:03:23,290 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:03:23,290 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:03:23,561 - app.utils.memory_management - INFO - Memory before cleanup: 483.10 MB
2025-05-25 03:03:23,685 - app.utils.memory_management - INFO - Garbage collection: collected 1592 objects
2025-05-25 03:03:23,685 - app.utils.memory_management - INFO - Memory after cleanup: 483.10 MB (freed 0.00 MB)
2025-05-25 03:03:24,141 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:24,162 - app.utils.memory_management - INFO - Memory before cleanup: 483.02 MB
2025-05-25 03:03:24,286 - app.utils.memory_management - INFO - Garbage collection: collected 236 objects
2025-05-25 03:03:24,286 - app.utils.memory_management - INFO - Memory after cleanup: 483.02 MB (freed 0.00 MB)
2025-05-25 03:03:25,923 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:25,967 - app.utils.memory_management - INFO - Memory before cleanup: 483.02 MB
2025-05-25 03:03:26,154 - app.utils.memory_management - INFO - Garbage collection: collected 194 objects
2025-05-25 03:03:26,154 - app.utils.memory_management - INFO - Memory after cleanup: 483.02 MB (freed 0.00 MB)
2025-05-25 03:03:27,007 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:27,122 - app.utils.memory_management - INFO - Memory before cleanup: 483.02 MB
2025-05-25 03:03:27,459 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-25 03:03:27,460 - app.utils.memory_management - INFO - Memory after cleanup: 483.02 MB (freed 0.01 MB)
2025-05-25 03:03:30,117 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:30,148 - app.utils.memory_management - INFO - Memory before cleanup: 483.02 MB
2025-05-25 03:03:30,275 - app.utils.memory_management - INFO - Garbage collection: collected 206 objects
2025-05-25 03:03:30,275 - app.utils.memory_management - INFO - Memory after cleanup: 483.02 MB (freed 0.00 MB)
2025-05-25 03:03:31,088 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:31,103 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 03:03:32,295 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 03:03:32,328 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-25 03:03:33,962 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:33,979 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 03:03:34,461 - app.utils.memory_management - INFO - Memory before cleanup: 483.12 MB
2025-05-25 03:03:34,579 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-25 03:03:34,579 - app.utils.memory_management - INFO - Memory after cleanup: 483.14 MB (freed -0.01 MB)
2025-05-25 03:03:35,020 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:03:35,137 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:03:35,179 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:03:35,185 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:03:35,185 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:03:35,186 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:03:35,242 - app.utils.memory_management - INFO - Memory before cleanup: 483.16 MB
2025-05-25 03:03:35,392 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-25 03:03:35,393 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 03:03:35,393 - app.utils.memory_management - INFO - Memory after cleanup: 483.19 MB (freed -0.04 MB)
2025-05-25 03:03:35,394 - app.utils.memory_management - INFO - Memory before cleanup: 483.19 MB
2025-05-25 03:03:35,725 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-25 03:03:35,726 - app.utils.memory_management - INFO - Memory after cleanup: 483.19 MB (freed 0.00 MB)
2025-05-25 03:03:35,727 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-25 03:04:08,848 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:08,899 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:04:08,930 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:04:08,932 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:04:08,932 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:04:08,932 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:04:08,979 - app.utils.memory_management - INFO - Memory before cleanup: 483.09 MB
2025-05-25 03:04:09,129 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-25 03:04:09,129 - app.utils.memory_management - INFO - Memory after cleanup: 483.09 MB (freed 0.00 MB)
2025-05-25 03:04:09,713 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:09,725 - app.utils.memory_management - INFO - Memory before cleanup: 483.09 MB
2025-05-25 03:04:09,846 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-25 03:04:09,846 - app.utils.memory_management - INFO - Memory after cleanup: 483.09 MB (freed 0.00 MB)
2025-05-25 03:04:28,758 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:28,775 - app.utils.memory_management - INFO - Memory before cleanup: 483.08 MB
2025-05-25 03:04:28,898 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-25 03:04:28,898 - app.utils.memory_management - INFO - Memory after cleanup: 483.08 MB (freed 0.00 MB)
2025-05-25 03:04:29,036 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:29,060 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:04:29,061 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:04:29,061 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:04:29,061 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:04:29,298 - app.utils.memory_management - INFO - Memory before cleanup: 483.17 MB
2025-05-25 03:04:29,433 - app.utils.memory_management - INFO - Garbage collection: collected 1529 objects
2025-05-25 03:04:29,433 - app.utils.memory_management - INFO - Memory after cleanup: 483.17 MB (freed 0.00 MB)
2025-05-25 03:04:55,808 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:55,831 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:04:55,832 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:04:55,832 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:04:55,832 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:04:56,101 - app.utils.memory_management - INFO - Memory before cleanup: 483.96 MB
2025-05-25 03:04:56,257 - app.utils.memory_management - INFO - Garbage collection: collected 1678 objects
2025-05-25 03:04:56,257 - app.utils.memory_management - INFO - Memory after cleanup: 483.96 MB (freed 0.00 MB)
2025-05-25 03:04:57,216 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:57,242 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:04:57,242 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:04:57,243 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:04:57,243 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:04:57,556 - app.utils.memory_management - INFO - Memory before cleanup: 483.97 MB
2025-05-25 03:04:57,675 - app.utils.memory_management - INFO - Garbage collection: collected 2581 objects
2025-05-25 03:04:57,675 - app.utils.memory_management - INFO - Memory after cleanup: 483.97 MB (freed 0.00 MB)
2025-05-25 03:04:58,563 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:04:58,589 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 03:04:58,590 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:04:58,590 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:04:58,590 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:04:58,900 - app.utils.memory_management - INFO - Memory before cleanup: 483.98 MB
2025-05-25 03:04:59,010 - app.utils.memory_management - INFO - Garbage collection: collected 2590 objects
2025-05-25 03:04:59,010 - app.utils.memory_management - INFO - Memory after cleanup: 483.98 MB (freed 0.00 MB)
2025-05-25 03:05:00,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:05:00,344 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-25 03:05:00,344 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 03:05:00,344 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 03:05:00,344 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 03:05:00,686 - app.utils.memory_management - INFO - Memory before cleanup: 483.98 MB
2025-05-25 03:05:00,812 - app.utils.memory_management - INFO - Garbage collection: collected 2621 objects
2025-05-25 03:05:00,812 - app.utils.memory_management - INFO - Memory after cleanup: 483.98 MB (freed 0.00 MB)
2025-05-25 03:06:43,963 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:06:43,986 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 03:06:44,249 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-25 03:06:44,434 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:06:44,441 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-25 03:06:44,442 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 03:06:44,443 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-25 03:06:44,445 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-25 03:06:44,629 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-25 03:06:44,835 - app.utils.memory_management - INFO - Memory before cleanup: 484.11 MB
2025-05-25 03:06:44,946 - app.utils.memory_management - INFO - Garbage collection: collected 3351 objects
2025-05-25 03:06:44,946 - app.utils.memory_management - INFO - Memory after cleanup: 484.11 MB (freed 0.00 MB)
2025-05-25 03:06:55,150 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:06:55,398 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-25 03:06:55,557 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-25 03:06:55,564 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-25 03:06:55,565 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-25 03:06:55,565 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-25 03:06:55,566 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-25 03:06:55,783 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.23 seconds
2025-05-25 03:06:55,963 - app.utils.memory_management - INFO - Memory before cleanup: 484.36 MB
2025-05-25 03:06:56,085 - app.utils.memory_management - INFO - Garbage collection: collected 2894 objects
2025-05-25 03:06:56,085 - app.utils.memory_management - INFO - Memory after cleanup: 484.36 MB (freed 0.00 MB)
2025-05-25 03:07:44,340 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:07:44,370 - app.utils.memory_management - INFO - Memory before cleanup: 484.32 MB
2025-05-25 03:07:44,518 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-25 03:07:44,519 - app.utils.memory_management - INFO - Memory after cleanup: 484.32 MB (freed 0.00 MB)
2025-05-25 03:18:11,778 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:18:11,798 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 03:18:11,838 - app - INFO - File COMI contains 2025 data
2025-05-25 03:18:11,845 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-25 03:18:11,846 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-25 03:18:11,848 - app.utils.memory_management - INFO - Memory before cleanup: 480.51 MB
2025-05-25 03:18:12,043 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-25 03:18:12,043 - app.utils.memory_management - INFO - Memory after cleanup: 480.51 MB (freed -0.00 MB)
2025-05-25 03:18:23,174 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:18:23,205 - app - INFO - File COMI contains 2025 data
2025-05-25 03:18:23,221 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-25 03:18:23,221 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-25 03:18:23,223 - app.utils.memory_management - INFO - Memory before cleanup: 480.54 MB
2025-05-25 03:18:23,353 - app.utils.memory_management - INFO - Garbage collection: collected 232 objects
2025-05-25 03:18:23,354 - app.utils.memory_management - INFO - Memory after cleanup: 480.54 MB (freed 0.00 MB)
2025-05-25 03:18:27,664 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:18:27,716 - app - INFO - File COMI contains 2025 data
2025-05-25 03:18:27,721 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-25 03:18:27,722 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-25 03:18:27,724 - app.utils.memory_management - INFO - Memory before cleanup: 480.55 MB
2025-05-25 03:18:27,842 - app.utils.memory_management - INFO - Garbage collection: collected 232 objects
2025-05-25 03:18:27,842 - app.utils.memory_management - INFO - Memory after cleanup: 480.55 MB (freed 0.00 MB)
2025-05-25 03:18:28,655 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:18:28,705 - app - INFO - File COMI contains 2025 data
2025-05-25 03:18:28,712 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-25 03:18:28,712 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-25 03:18:28,714 - app.utils.memory_management - INFO - Memory before cleanup: 480.56 MB
2025-05-25 03:18:28,879 - app.utils.memory_management - INFO - Garbage collection: collected 254 objects
2025-05-25 03:18:28,880 - app.utils.memory_management - INFO - Memory after cleanup: 480.56 MB (freed 0.00 MB)
2025-05-25 03:18:32,566 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 03:18:32,636 - app - ERROR - Error processing CSV file: Can only use .dt accessor with datetimelike values
2025-05-25 03:18:32,636 - app.utils.memory_management - INFO - Memory before cleanup: 480.57 MB
2025-05-25 03:18:32,763 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-05-25 03:18:32,763 - app.utils.memory_management - INFO - Memory after cleanup: 480.57 MB (freed 0.00 MB)
