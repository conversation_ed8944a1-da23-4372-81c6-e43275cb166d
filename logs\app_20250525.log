2025-05-25 00:58:56,292 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 00:58:57,452 - app - INFO - Memory management utilities loaded
2025-05-25 00:58:57,452 - app - INFO - Error handling utilities loaded
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy fix
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 00:59:00,875 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 00:59:00,875 - app - INFO - Applied TensorFlow fix
2025-05-25 00:59:00,878 - app.config - INFO - Configuration initialized
2025-05-25 00:59:00,881 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 00:59:00,888 - models.train - INFO - TensorFlow test successful
2025-05-25 00:59:01,298 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 00:59:01,299 - models.train - INFO - Transformer model is available
2025-05-25 00:59:01,299 - models.train - INFO - Using TensorFlow-based models
2025-05-25 00:59:01,300 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 00:59:01,301 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 00:59:01,302 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:59:01,636 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 00:59:01,639 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:01,920 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 00:59:02,407 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 00:59:02,436 - app.utils.session_state - INFO - Initializing session state
2025-05-25 00:59:02,438 - app.utils.session_state - INFO - Session state initialized
2025-05-25 00:59:03,411 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 00:59:03,418 - app.utils.memory_management - INFO - Memory before cleanup: 429.14 MB
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Memory after cleanup: 429.14 MB (freed -0.00 MB)
2025-05-25 00:59:15,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:15,598 - app.utils.memory_management - INFO - Memory before cleanup: 432.43 MB
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Memory after cleanup: 432.43 MB (freed 0.00 MB)
2025-05-25 00:59:16,669 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:16,701 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 00:59:16,701 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,702 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,702 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,721 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 00:59:16,721 - app - INFO - Features shape: (572, 36)
2025-05-25 00:59:16,738 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:16,739 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,739 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,739 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,759 - app.utils.memory_management - INFO - Memory before cleanup: 437.18 MB
2025-05-25 00:59:16,879 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-05-25 00:59:16,881 - app.utils.memory_management - INFO - Memory after cleanup: 437.21 MB (freed -0.04 MB)
2025-05-25 00:59:23,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:23,604 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:23,605 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:23,605 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 00:59:23,606 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 00:59:24,353 - app.utils.memory_management - INFO - Memory before cleanup: 442.44 MB
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Garbage collection: collected 1059 objects
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed 0.00 MB)
2025-05-25 01:03:14,045 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:14,141 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.06 seconds
2025-05-25 01:03:14,145 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:14,145 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:14,146 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:14,401 - app.utils.memory_management - INFO - Memory before cleanup: 445.73 MB
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Garbage collection: collected 1087 objects
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Memory after cleanup: 445.73 MB (freed 0.00 MB)
2025-05-25 01:03:21,135 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:21,160 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:21,160 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:21,160 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:21,160 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:21,404 - app.utils.memory_management - INFO - Memory before cleanup: 446.55 MB
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Garbage collection: collected 924 objects
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Memory after cleanup: 446.55 MB (freed 0.00 MB)
2025-05-25 01:03:42,488 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:42,524 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:42,525 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:42,525 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:42,526 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:42,804 - app.utils.memory_management - INFO - Memory before cleanup: 446.64 MB
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Garbage collection: collected 595 objects
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Memory after cleanup: 446.64 MB (freed 0.00 MB)
2025-05-25 01:05:07,325 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:05:07,334 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:05:07,368 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:05:07,369 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:05:07,370 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:05:07,370 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:05:07,654 - app.utils.memory_management - INFO - Memory before cleanup: 448.05 MB
2025-05-25 01:05:07,775 - app.utils.memory_management - INFO - Garbage collection: collected 1632 objects
2025-05-25 01:05:07,786 - app.utils.memory_management - INFO - Memory after cleanup: 448.05 MB (freed 0.00 MB)
2025-05-25 01:06:05,259 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:05,310 - app.utils.demo_data_generator - INFO - Generated 91 days of demo data for COMI
2025-05-25 01:06:05,580 - app.utils.memory_management - INFO - Memory before cleanup: 448.18 MB
2025-05-25 01:06:05,699 - app.utils.memory_management - INFO - Garbage collection: collected 1188 objects
2025-05-25 01:06:05,700 - app.utils.memory_management - INFO - Memory after cleanup: 448.18 MB (freed 0.00 MB)
2025-05-25 01:06:24,129 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:24,154 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:06:24,155 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:06:24,155 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:06:24,155 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:06:24,457 - app.utils.memory_management - INFO - Memory before cleanup: 448.20 MB
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Garbage collection: collected 1560 objects
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Memory after cleanup: 448.20 MB (freed 0.00 MB)
2025-05-25 01:08:23,275 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:23,321 - app.utils.memory_management - INFO - Memory before cleanup: 448.13 MB
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Garbage collection: collected 263 objects
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Memory after cleanup: 448.13 MB (freed 0.00 MB)
2025-05-25 01:08:26,534 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:26,556 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:08:26,557 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:08:26,557 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:08:26,558 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:08:26,750 - app.utils.memory_management - INFO - Memory before cleanup: 448.19 MB
2025-05-25 01:08:26,900 - app.utils.memory_management - INFO - Garbage collection: collected 1073 objects
2025-05-25 01:08:26,909 - app.utils.memory_management - INFO - Memory after cleanup: 448.19 MB (freed 0.00 MB)
2025-05-25 01:08:42,412 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:42,439 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:42,556 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-25 01:08:42,557 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:47,053 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:47,079 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:56,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:56,507 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 01:08:57,701 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 01:08:57,797 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:08:57,802 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:08:57,804 - app.utils.error_handling - INFO - live_trading_component executed in 1.30 seconds
2025-05-25 01:08:57,807 - app.utils.memory_management - INFO - Memory before cleanup: 450.18 MB
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Memory after cleanup: 450.18 MB (freed 0.00 MB)
2025-05-25 01:09:04,227 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:09:04,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:09:04,251 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-25 01:09:04,252 - app.utils.memory_management - INFO - Memory before cleanup: 450.70 MB
2025-05-25 01:09:04,366 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-25 01:09:04,369 - app.utils.memory_management - INFO - Memory after cleanup: 450.70 MB (freed 0.00 MB)
2025-05-25 01:09:05,613 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:05,636 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:05,637 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:05,637 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:05,637 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:05,832 - app.utils.memory_management - INFO - Memory before cleanup: 452.22 MB
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Garbage collection: collected 1035 objects
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Memory after cleanup: 452.22 MB (freed 0.00 MB)
2025-05-25 01:09:39,004 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:39,048 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:39,048 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:39,049 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:39,049 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:39,329 - app.utils.memory_management - INFO - Memory before cleanup: 452.28 MB
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Garbage collection: collected 948 objects
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Memory after cleanup: 452.28 MB (freed 0.00 MB)
2025-05-25 01:09:41,052 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:41,075 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:41,076 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:41,076 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:41,077 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:41,326 - app.utils.memory_management - INFO - Memory before cleanup: 452.30 MB
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Garbage collection: collected 1670 objects
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Memory after cleanup: 452.30 MB (freed 0.00 MB)
2025-05-25 01:09:42,323 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:42,350 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:42,351 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:42,352 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:42,352 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:42,599 - app.utils.memory_management - INFO - Memory before cleanup: 452.31 MB
2025-05-25 01:09:42,709 - app.utils.memory_management - INFO - Garbage collection: collected 1863 objects
2025-05-25 01:09:42,710 - app.utils.memory_management - INFO - Memory after cleanup: 452.31 MB (freed 0.00 MB)
2025-05-25 01:09:44,076 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:44,100 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:44,100 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:44,101 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:44,101 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:44,345 - app.utils.memory_management - INFO - Memory before cleanup: 452.84 MB
2025-05-25 01:09:44,458 - app.utils.memory_management - INFO - Garbage collection: collected 1880 objects
2025-05-25 01:09:44,459 - app.utils.memory_management - INFO - Memory after cleanup: 452.84 MB (freed 0.00 MB)
2025-05-25 01:10:42,761 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:10:42,769 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:10:42,815 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:10:42,815 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:10:42,815 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:10:42,816 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:10:43,122 - app.utils.memory_management - INFO - Memory before cleanup: 452.85 MB
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Garbage collection: collected 1868 objects
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Memory after cleanup: 452.85 MB (freed 0.00 MB)
2025-05-25 01:17:34,876 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 01:17:34,876 - app - INFO - Memory management utilities loaded
2025-05-25 01:17:34,893 - app - INFO - Error handling utilities loaded
2025-05-25 01:17:34,893 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 01:17:34,895 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 01:17:34,954 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 01:17:34,986 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
