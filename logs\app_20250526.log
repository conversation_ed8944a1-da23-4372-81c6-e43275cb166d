2025-05-26 11:52:59,874 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-26 11:53:05,395 - app - INFO - Memory management utilities loaded
2025-05-26 11:53:05,406 - app - INFO - Error handling utilities loaded
2025-05-26 11:53:05,412 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-26 11:53:05,414 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-26 11:53:05,414 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-26 11:53:05,415 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-26 11:53:05,426 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-26 11:53:05,440 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-26 11:53:05,441 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-26 11:53:05,441 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-26 11:53:05,441 - app - INFO - Applied NumPy fix
2025-05-26 11:53:05,453 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-26 11:53:05,454 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-26 11:53:05,455 - app - INFO - Applied NumPy BitGenerator fix
2025-05-26 11:53:23,666 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-26 11:53:23,667 - app - INFO - Applied TensorFlow fix
2025-05-26 11:53:23,687 - app.config - INFO - Configuration initialized
2025-05-26 11:53:23,705 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-26 11:53:24,011 - models.train - INFO - TensorFlow test successful
2025-05-26 11:53:28,521 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-26 11:53:28,521 - models.train - INFO - Transformer model is available
2025-05-26 11:53:28,521 - models.train - INFO - Using TensorFlow-based models
2025-05-26 11:53:28,530 - models.predict - INFO - Transformer model is available for predictions
2025-05-26 11:53:28,531 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-26 11:53:28,559 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-26 11:53:30,584 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-26 11:53:30,585 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-26 11:53:30,587 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-26 11:53:31,624 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-26 11:53:31,640 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:32,485 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-26 11:53:35,792 - app.services.llm_service - INFO - llama_cpp is available
2025-05-26 11:53:35,837 - app.utils.session_state - INFO - Initializing session state
2025-05-26 11:53:35,838 - app.utils.session_state - INFO - Session state initialized
2025-05-26 11:53:36,986 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 11:53:36,996 - app.utils.memory_management - INFO - Memory before cleanup: 427.66 MB
2025-05-26 11:53:37,117 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-26 11:53:37,117 - app.utils.memory_management - INFO - Memory after cleanup: 427.66 MB (freed -0.00 MB)
2025-05-26 11:53:54,220 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:54,252 - app.utils.memory_management - INFO - Memory before cleanup: 429.23 MB
2025-05-26 11:53:54,412 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-26 11:53:54,413 - app.utils.memory_management - INFO - Memory after cleanup: 429.25 MB (freed -0.03 MB)
2025-05-26 11:53:55,469 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:55,599 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.09 seconds
2025-05-26 11:53:55,619 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 11:53:55,619 - app - INFO - Data shape: (576, 6)
2025-05-26 11:53:55,619 - app - INFO - File COMI contains 2025 data
2025-05-26 11:53:55,651 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-26 11:53:55,651 - app - INFO - Features shape: (576, 36)
2025-05-26 11:53:55,671 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-26 11:53:55,671 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 11:53:55,672 - app - INFO - Data shape: (576, 6)
2025-05-26 11:53:55,672 - app - INFO - File COMI contains 2025 data
2025-05-26 11:53:55,674 - app.utils.memory_management - INFO - Memory before cleanup: 432.84 MB
2025-05-26 11:53:55,802 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-05-26 11:53:55,802 - app.utils.memory_management - INFO - Memory after cleanup: 432.88 MB (freed -0.04 MB)
2025-05-26 11:53:55,936 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:56,020 - app.utils.memory_management - INFO - Memory before cleanup: 433.80 MB
2025-05-26 11:53:56,144 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-26 11:53:56,145 - app.utils.memory_management - INFO - Memory after cleanup: 433.80 MB (freed 0.00 MB)
2025-05-26 11:54:30,116 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:54:30,150 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:54:31,498 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:54:31,719 - app.utils.error_handling - INFO - live_trading_component executed in 1.58 seconds
2025-05-26 11:54:31,719 - app.utils.memory_management - INFO - Memory before cleanup: 436.54 MB
2025-05-26 11:54:31,865 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-26 11:54:31,866 - app.utils.memory_management - INFO - Memory after cleanup: 436.54 MB (freed 0.00 MB)
2025-05-26 11:55:10,663 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:55:10,696 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:55:13,023 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:55:13,023 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:55:14,160 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:55:14,170 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:55:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:55:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:55:14,172 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:55:14,175 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:55:14,180 - app.utils.error_handling - INFO - live_trading_component executed in 3.50 seconds
2025-05-26 11:55:14,181 - app.utils.memory_management - INFO - Memory before cleanup: 437.75 MB
2025-05-26 11:55:14,304 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-05-26 11:55:14,305 - app.utils.memory_management - INFO - Memory after cleanup: 437.75 MB (freed 0.00 MB)
2025-05-26 11:56:17,243 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:56:17,318 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:56:17,319 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:56:17,321 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:56:17,322 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:56:17,327 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:56:17,333 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-26 11:56:17,334 - app.utils.memory_management - INFO - Memory before cleanup: 432.80 MB
2025-05-26 11:56:17,503 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-26 11:56:17,503 - app.utils.memory_management - INFO - Memory after cleanup: 432.89 MB (freed -0.10 MB)
2025-05-26 11:57:36,843 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:57:36,869 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:57:38,984 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:57:38,984 - scrapers.price_scraper - INFO - Account type: Standard TradingView
2025-05-26 11:57:38,984 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:57:40,165 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:57:40,174 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:57:40,175 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:57:40,176 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:57:40,177 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:57:40,182 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:57:40,183 - app.utils.error_handling - INFO - live_trading_component executed in 3.33 seconds
2025-05-26 11:57:40,185 - app.utils.memory_management - INFO - Memory before cleanup: 432.97 MB
2025-05-26 11:57:40,310 - app.utils.memory_management - INFO - Garbage collection: collected 252 objects
2025-05-26 11:57:40,311 - app.utils.memory_management - INFO - Memory after cleanup: 432.97 MB (freed 0.00 MB)
