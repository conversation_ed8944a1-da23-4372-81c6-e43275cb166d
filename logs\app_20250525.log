2025-05-25 00:58:56,292 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 00:58:57,452 - app - INFO - Memory management utilities loaded
2025-05-25 00:58:57,452 - app - INFO - Error handling utilities loaded
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy fix
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 00:59:00,875 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 00:59:00,875 - app - INFO - Applied TensorFlow fix
2025-05-25 00:59:00,878 - app.config - INFO - Configuration initialized
2025-05-25 00:59:00,881 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 00:59:00,888 - models.train - INFO - TensorFlow test successful
2025-05-25 00:59:01,298 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 00:59:01,299 - models.train - INFO - Transformer model is available
2025-05-25 00:59:01,299 - models.train - INFO - Using TensorFlow-based models
2025-05-25 00:59:01,300 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 00:59:01,301 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 00:59:01,302 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 00:59:01,558 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:59:01,559 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:59:01,636 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 00:59:01,639 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:01,920 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 00:59:02,407 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 00:59:02,436 - app.utils.session_state - INFO - Initializing session state
2025-05-25 00:59:02,438 - app.utils.session_state - INFO - Session state initialized
2025-05-25 00:59:03,411 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 00:59:03,418 - app.utils.memory_management - INFO - Memory before cleanup: 429.14 MB
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:03,578 - app.utils.memory_management - INFO - Memory after cleanup: 429.14 MB (freed -0.00 MB)
2025-05-25 00:59:15,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:15,598 - app.utils.memory_management - INFO - Memory before cleanup: 432.43 MB
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 00:59:15,728 - app.utils.memory_management - INFO - Memory after cleanup: 432.43 MB (freed 0.00 MB)
2025-05-25 00:59:16,669 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:16,701 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 00:59:16,701 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,702 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,702 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,721 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 00:59:16,721 - app - INFO - Features shape: (572, 36)
2025-05-25 00:59:16,738 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:16,739 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:16,739 - app - INFO - Data shape: (572, 36)
2025-05-25 00:59:16,739 - app - INFO - File COMI contains 2025 data
2025-05-25 00:59:16,759 - app.utils.memory_management - INFO - Memory before cleanup: 437.18 MB
2025-05-25 00:59:16,879 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-05-25 00:59:16,881 - app.utils.memory_management - INFO - Memory after cleanup: 437.21 MB (freed -0.04 MB)
2025-05-25 00:59:23,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 00:59:23,604 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 00:59:23,605 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 00:59:23,605 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 00:59:23,606 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 00:59:24,353 - app.utils.memory_management - INFO - Memory before cleanup: 442.44 MB
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Garbage collection: collected 1059 objects
2025-05-25 00:59:24,466 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed 0.00 MB)
2025-05-25 01:03:14,045 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:14,141 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.06 seconds
2025-05-25 01:03:14,145 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:14,145 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:14,146 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:14,401 - app.utils.memory_management - INFO - Memory before cleanup: 445.73 MB
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Garbage collection: collected 1087 objects
2025-05-25 01:03:14,543 - app.utils.memory_management - INFO - Memory after cleanup: 445.73 MB (freed 0.00 MB)
2025-05-25 01:03:21,135 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:21,160 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:21,160 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:21,160 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:21,160 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:21,404 - app.utils.memory_management - INFO - Memory before cleanup: 446.55 MB
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Garbage collection: collected 924 objects
2025-05-25 01:03:21,515 - app.utils.memory_management - INFO - Memory after cleanup: 446.55 MB (freed 0.00 MB)
2025-05-25 01:03:42,488 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:03:42,524 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:03:42,525 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:03:42,525 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:03:42,526 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:03:42,804 - app.utils.memory_management - INFO - Memory before cleanup: 446.64 MB
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Garbage collection: collected 595 objects
2025-05-25 01:03:42,907 - app.utils.memory_management - INFO - Memory after cleanup: 446.64 MB (freed 0.00 MB)
2025-05-25 01:05:07,325 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:05:07,334 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:05:07,368 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:05:07,369 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:05:07,370 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:05:07,370 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:05:07,654 - app.utils.memory_management - INFO - Memory before cleanup: 448.05 MB
2025-05-25 01:05:07,775 - app.utils.memory_management - INFO - Garbage collection: collected 1632 objects
2025-05-25 01:05:07,786 - app.utils.memory_management - INFO - Memory after cleanup: 448.05 MB (freed 0.00 MB)
2025-05-25 01:06:05,259 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:05,310 - app.utils.demo_data_generator - INFO - Generated 91 days of demo data for COMI
2025-05-25 01:06:05,580 - app.utils.memory_management - INFO - Memory before cleanup: 448.18 MB
2025-05-25 01:06:05,699 - app.utils.memory_management - INFO - Garbage collection: collected 1188 objects
2025-05-25 01:06:05,700 - app.utils.memory_management - INFO - Memory after cleanup: 448.18 MB (freed 0.00 MB)
2025-05-25 01:06:24,129 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:06:24,154 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:06:24,155 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:06:24,155 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:06:24,155 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:06:24,457 - app.utils.memory_management - INFO - Memory before cleanup: 448.20 MB
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Garbage collection: collected 1560 objects
2025-05-25 01:06:24,567 - app.utils.memory_management - INFO - Memory after cleanup: 448.20 MB (freed 0.00 MB)
2025-05-25 01:08:23,275 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:23,321 - app.utils.memory_management - INFO - Memory before cleanup: 448.13 MB
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Garbage collection: collected 263 objects
2025-05-25 01:08:23,454 - app.utils.memory_management - INFO - Memory after cleanup: 448.13 MB (freed 0.00 MB)
2025-05-25 01:08:26,534 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:26,556 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:08:26,557 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:08:26,557 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:08:26,558 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:08:26,750 - app.utils.memory_management - INFO - Memory before cleanup: 448.19 MB
2025-05-25 01:08:26,900 - app.utils.memory_management - INFO - Garbage collection: collected 1073 objects
2025-05-25 01:08:26,909 - app.utils.memory_management - INFO - Memory after cleanup: 448.19 MB (freed 0.00 MB)
2025-05-25 01:08:42,412 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:42,439 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:42,556 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-25 01:08:42,557 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:47,053 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:47,079 - app.utils.memory_management - INFO - Memory before cleanup: 448.14 MB
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:47,205 - app.utils.memory_management - INFO - Memory after cleanup: 448.14 MB (freed 0.00 MB)
2025-05-25 01:08:56,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:08:56,507 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 01:08:57,701 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 01:08:57,797 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:08:57,798 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:08:57,802 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:08:57,804 - app.utils.error_handling - INFO - live_trading_component executed in 1.30 seconds
2025-05-25 01:08:57,807 - app.utils.memory_management - INFO - Memory before cleanup: 450.18 MB
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-25 01:08:57,917 - app.utils.memory_management - INFO - Memory after cleanup: 450.18 MB (freed 0.00 MB)
2025-05-25 01:09:04,227 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:09:04,244 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:09:04,245 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:09:04,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:09:04,251 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-25 01:09:04,252 - app.utils.memory_management - INFO - Memory before cleanup: 450.70 MB
2025-05-25 01:09:04,366 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-25 01:09:04,369 - app.utils.memory_management - INFO - Memory after cleanup: 450.70 MB (freed 0.00 MB)
2025-05-25 01:09:05,613 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:05,636 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:05,637 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:05,637 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:05,637 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:05,832 - app.utils.memory_management - INFO - Memory before cleanup: 452.22 MB
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Garbage collection: collected 1035 objects
2025-05-25 01:09:05,939 - app.utils.memory_management - INFO - Memory after cleanup: 452.22 MB (freed 0.00 MB)
2025-05-25 01:09:39,004 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:39,048 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:39,048 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:39,049 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:39,049 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:39,329 - app.utils.memory_management - INFO - Memory before cleanup: 452.28 MB
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Garbage collection: collected 948 objects
2025-05-25 01:09:39,466 - app.utils.memory_management - INFO - Memory after cleanup: 452.28 MB (freed 0.00 MB)
2025-05-25 01:09:41,052 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:41,075 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:41,076 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:41,076 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:41,077 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:41,326 - app.utils.memory_management - INFO - Memory before cleanup: 452.30 MB
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Garbage collection: collected 1670 objects
2025-05-25 01:09:41,434 - app.utils.memory_management - INFO - Memory after cleanup: 452.30 MB (freed 0.00 MB)
2025-05-25 01:09:42,323 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:42,350 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:42,351 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:42,352 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:42,352 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:42,599 - app.utils.memory_management - INFO - Memory before cleanup: 452.31 MB
2025-05-25 01:09:42,709 - app.utils.memory_management - INFO - Garbage collection: collected 1863 objects
2025-05-25 01:09:42,710 - app.utils.memory_management - INFO - Memory after cleanup: 452.31 MB (freed 0.00 MB)
2025-05-25 01:09:44,076 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:09:44,100 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:09:44,100 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:09:44,101 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:09:44,101 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:09:44,345 - app.utils.memory_management - INFO - Memory before cleanup: 452.84 MB
2025-05-25 01:09:44,458 - app.utils.memory_management - INFO - Garbage collection: collected 1880 objects
2025-05-25 01:09:44,459 - app.utils.memory_management - INFO - Memory after cleanup: 452.84 MB (freed 0.00 MB)
2025-05-25 01:10:42,761 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:10:42,769 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:10:42,815 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:10:42,815 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:10:42,815 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:10:42,816 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:10:43,122 - app.utils.memory_management - INFO - Memory before cleanup: 452.85 MB
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Garbage collection: collected 1868 objects
2025-05-25 01:10:43,243 - app.utils.memory_management - INFO - Memory after cleanup: 452.85 MB (freed 0.00 MB)
2025-05-25 01:17:34,876 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 01:17:34,876 - app - INFO - Memory management utilities loaded
2025-05-25 01:17:34,893 - app - INFO - Error handling utilities loaded
2025-05-25 01:17:34,893 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 01:17:34,895 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 01:17:34,954 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 01:17:34,986 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 01:19:06,307 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 01:19:07,229 - app - INFO - Memory management utilities loaded
2025-05-25 01:19:07,232 - app - INFO - Error handling utilities loaded
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 01:19:07,232 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 01:19:07,235 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 01:19:07,235 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 01:19:07,235 - app - INFO - Applied NumPy fix
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 01:19:07,235 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 01:19:07,235 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 01:19:09,863 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 01:19:09,863 - app - INFO - Applied TensorFlow fix
2025-05-25 01:19:09,866 - app.config - INFO - Configuration initialized
2025-05-25 01:19:09,869 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 01:19:09,876 - models.train - INFO - TensorFlow test successful
2025-05-25 01:19:10,210 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 01:19:10,211 - models.train - INFO - Transformer model is available
2025-05-25 01:19:10,211 - models.train - INFO - Using TensorFlow-based models
2025-05-25 01:19:10,212 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 01:19:10,212 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 01:19:10,213 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 01:19:10,434 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 01:19:10,434 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 01:19:10,435 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 01:19:10,436 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 01:19:10,492 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 01:19:10,493 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:10,740 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 01:19:11,126 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 01:19:11,141 - app.utils.session_state - INFO - Initializing session state
2025-05-25 01:19:11,142 - app.utils.session_state - INFO - Session state initialized
2025-05-25 01:19:12,373 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 01:19:12,381 - app.utils.memory_management - INFO - Memory before cleanup: 428.32 MB
2025-05-25 01:19:12,499 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 01:19:12,501 - app.utils.memory_management - INFO - Memory after cleanup: 428.32 MB (freed -0.01 MB)
2025-05-25 01:19:18,606 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:18,639 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 01:19:18,641 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:18,641 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:18,643 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:19,564 - app.utils.memory_management - INFO - Memory before cleanup: 453.61 MB
2025-05-25 01:19:19,675 - app.utils.memory_management - INFO - Garbage collection: collected 2370 objects
2025-05-25 01:19:19,676 - app.utils.memory_management - INFO - Memory after cleanup: 453.66 MB (freed -0.06 MB)
2025-05-25 01:19:26,578 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:26,609 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:26,609 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:26,609 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:26,610 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:26,859 - app.utils.memory_management - INFO - Memory before cleanup: 457.00 MB
2025-05-25 01:19:26,992 - app.utils.memory_management - INFO - Garbage collection: collected 1596 objects
2025-05-25 01:19:26,992 - app.utils.memory_management - INFO - Memory after cleanup: 457.04 MB (freed -0.04 MB)
2025-05-25 01:19:27,718 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:27,747 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:27,750 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:27,750 - app - INFO - Data shape: (572, 36)
2025-05-25 01:19:27,750 - app - INFO - File COMI contains 2025 data
2025-05-25 01:19:27,771 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 01:19:27,771 - app - INFO - Features shape: (572, 36)
2025-05-25 01:19:27,785 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:27,786 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:27,786 - app - INFO - Data shape: (572, 36)
2025-05-25 01:19:27,786 - app - INFO - File COMI contains 2025 data
2025-05-25 01:19:27,805 - app.utils.memory_management - INFO - Memory before cleanup: 459.75 MB
2025-05-25 01:19:27,925 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-05-25 01:19:27,925 - app.utils.memory_management - INFO - Memory after cleanup: 459.75 MB (freed 0.00 MB)
2025-05-25 01:19:51,125 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:51,160 - app.utils.memory_management - INFO - Memory before cleanup: 458.09 MB
2025-05-25 01:19:51,296 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-25 01:19:51,296 - app.utils.memory_management - INFO - Memory after cleanup: 458.09 MB (freed 0.00 MB)
2025-05-25 01:19:53,582 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:53,599 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-25 01:19:54,768 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-25 01:19:54,804 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:19:54,804 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:19:54,805 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:19:54,805 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:19:54,810 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:19:54,813 - app.utils.error_handling - INFO - live_trading_component executed in 1.22 seconds
2025-05-25 01:19:54,814 - app.utils.memory_management - INFO - Memory before cleanup: 460.12 MB
2025-05-25 01:19:54,955 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-25 01:19:54,955 - app.utils.memory_management - INFO - Memory after cleanup: 460.12 MB (freed 0.00 MB)
2025-05-25 01:19:58,447 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:58,546 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-25 01:19:58,547 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-25 01:19:58,550 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-25 01:19:58,551 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-25 01:19:58,552 - app.utils.memory_management - INFO - Memory before cleanup: 461.04 MB
2025-05-25 01:19:58,680 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-25 01:19:58,681 - app.utils.memory_management - INFO - Memory after cleanup: 461.04 MB (freed 0.00 MB)
2025-05-25 01:19:59,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:19:59,438 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:19:59,438 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:19:59,439 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:19:59,439 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:19:59,698 - app.utils.memory_management - INFO - Memory before cleanup: 462.61 MB
2025-05-25 01:19:59,835 - app.utils.memory_management - INFO - Garbage collection: collected 1653 objects
2025-05-25 01:19:59,836 - app.utils.memory_management - INFO - Memory after cleanup: 462.61 MB (freed 0.00 MB)
2025-05-25 01:20:16,923 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:16,956 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:16,957 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:16,957 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:16,958 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:17,217 - app.utils.memory_management - INFO - Memory before cleanup: 463.61 MB
2025-05-25 01:20:17,381 - app.utils.memory_management - INFO - Garbage collection: collected 1680 objects
2025-05-25 01:20:17,385 - app.utils.memory_management - INFO - Memory after cleanup: 463.61 MB (freed 0.00 MB)
2025-05-25 01:20:18,711 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:18,742 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:18,743 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:18,743 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:18,743 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:19,079 - app.utils.memory_management - INFO - Memory before cleanup: 463.62 MB
2025-05-25 01:20:19,206 - app.utils.memory_management - INFO - Garbage collection: collected 2576 objects
2025-05-25 01:20:19,207 - app.utils.memory_management - INFO - Memory after cleanup: 463.62 MB (freed 0.00 MB)
2025-05-25 01:20:24,450 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:20:24,527 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:20:24,535 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:20:24,535 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:20:24,536 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:20:24,852 - app.utils.memory_management - INFO - Memory before cleanup: 464.10 MB
2025-05-25 01:20:24,964 - app.utils.memory_management - INFO - Garbage collection: collected 2476 objects
2025-05-25 01:20:24,965 - app.utils.memory_management - INFO - Memory after cleanup: 464.10 MB (freed 0.00 MB)
2025-05-25 01:21:34,912 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:21:34,950 - app.utils.common - INFO - Loaded stock data for SWDY from data/stocks\SWDY.csv in 0.00 seconds
2025-05-25 01:21:34,951 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-08
2025-05-25 01:21:34,951 - app.utils.common - INFO - Data shape: (307, 6)
2025-05-25 01:21:34,952 - app.utils.common - INFO - File SWDY contains 2025 data
2025-05-25 01:21:35,320 - app.utils.memory_management - INFO - Memory before cleanup: 464.08 MB
2025-05-25 01:21:35,438 - app.utils.memory_management - INFO - Garbage collection: collected 2536 objects
2025-05-25 01:21:35,439 - app.utils.memory_management - INFO - Memory after cleanup: 464.08 MB (freed 0.00 MB)
2025-05-25 01:21:40,556 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 01:21:40,582 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 01:21:40,583 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 01:21:40,583 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 01:21:40,584 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 01:21:40,886 - app.utils.memory_management - INFO - Memory before cleanup: 464.07 MB
2025-05-25 01:21:41,034 - app.utils.memory_management - INFO - Garbage collection: collected 2585 objects
2025-05-25 01:21:41,035 - app.utils.memory_management - INFO - Memory after cleanup: 464.07 MB (freed 0.00 MB)
2025-05-25 02:07:10,967 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:07:10,983 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 02:07:11,021 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:07:11,022 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:07:11,022 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:07:11,023 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:07:11,414 - app.utils.memory_management - INFO - Memory before cleanup: 459.91 MB
2025-05-25 02:07:11,541 - app.utils.memory_management - INFO - Garbage collection: collected 2620 objects
2025-05-25 02:07:11,542 - app.utils.memory_management - INFO - Memory after cleanup: 459.88 MB (freed 0.03 MB)
2025-05-25 02:12:50,211 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:12:50,215 - app - INFO - Memory management utilities loaded
2025-05-25 02:12:50,216 - app - INFO - Error handling utilities loaded
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:12:50,217 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:13:13,839 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:13:14,895 - app - INFO - Memory management utilities loaded
2025-05-25 02:13:14,898 - app - INFO - Error handling utilities loaded
2025-05-25 02:13:14,899 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:13:14,900 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:13:14,901 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:13:14,901 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:13:14,901 - app - INFO - Applied NumPy fix
2025-05-25 02:13:14,902 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:13:14,902 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:13:14,903 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:13:14,903 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:13:17,853 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:13:17,853 - app - INFO - Applied TensorFlow fix
2025-05-25 02:13:17,854 - app.config - INFO - Configuration initialized
2025-05-25 02:13:17,857 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:13:17,864 - models.train - INFO - TensorFlow test successful
2025-05-25 02:13:18,231 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:13:18,232 - models.train - INFO - Transformer model is available
2025-05-25 02:13:18,232 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:13:18,234 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:13:18,234 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:13:18,235 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:13:18,459 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:13:18,459 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:13:18,459 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:13:18,459 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:13:18,460 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:13:18,518 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:13:18,518 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:18,788 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:13:19,215 - app.services.llm_service - INFO - llama_cpp is available
2025-05-25 02:13:19,235 - app.utils.session_state - INFO - Initializing session state
2025-05-25 02:13:19,237 - app.utils.session_state - INFO - Session state initialized
2025-05-25 02:13:20,167 - app - INFO - Found 8 stock files in data/stocks
2025-05-25 02:13:20,177 - app.utils.memory_management - INFO - Memory before cleanup: 428.54 MB
2025-05-25 02:13:20,289 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-25 02:13:20,290 - app.utils.memory_management - INFO - Memory after cleanup: 428.55 MB (freed -0.00 MB)
2025-05-25 02:13:26,844 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:26,876 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:13:26,877 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:26,877 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:26,877 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:27,780 - app.utils.memory_management - INFO - Memory before cleanup: 453.90 MB
2025-05-25 02:13:27,887 - app.utils.memory_management - INFO - Garbage collection: collected 2388 objects
2025-05-25 02:13:27,887 - app.utils.memory_management - INFO - Memory after cleanup: 453.90 MB (freed 0.00 MB)
2025-05-25 02:13:37,447 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:37,475 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:37,476 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:37,476 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:37,476 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:37,720 - app.utils.memory_management - INFO - Memory before cleanup: 457.46 MB
2025-05-25 02:13:37,838 - app.utils.memory_management - INFO - Garbage collection: collected 1596 objects
2025-05-25 02:13:37,839 - app.utils.memory_management - INFO - Memory after cleanup: 457.50 MB (freed -0.04 MB)
2025-05-25 02:13:39,272 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:39,300 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:39,300 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:39,301 - app - INFO - Data shape: (572, 36)
2025-05-25 02:13:39,301 - app - INFO - File COMI contains 2025 data
2025-05-25 02:13:39,321 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-25 02:13:39,322 - app - INFO - Features shape: (572, 36)
2025-05-25 02:13:39,334 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:39,334 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:39,335 - app - INFO - Data shape: (572, 36)
2025-05-25 02:13:39,335 - app - INFO - File COMI contains 2025 data
2025-05-25 02:13:39,356 - app.utils.memory_management - INFO - Memory before cleanup: 459.63 MB
2025-05-25 02:13:39,476 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-05-25 02:13:39,477 - app.utils.memory_management - INFO - Memory after cleanup: 459.63 MB (freed 0.00 MB)
2025-05-25 02:13:44,748 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:44,761 - app.utils.memory_management - INFO - Memory before cleanup: 457.83 MB
2025-05-25 02:13:44,877 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-25 02:13:44,877 - app.utils.memory_management - INFO - Memory after cleanup: 457.83 MB (freed 0.00 MB)
2025-05-25 02:13:46,812 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:46,844 - app.utils.memory_management - INFO - Memory before cleanup: 457.86 MB
2025-05-25 02:13:46,954 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-25 02:13:46,954 - app.utils.memory_management - INFO - Memory after cleanup: 457.86 MB (freed 0.00 MB)
2025-05-25 02:13:49,289 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:13:49,315 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:13:49,316 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:13:49,316 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:13:49,317 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:13:49,548 - app.utils.memory_management - INFO - Memory before cleanup: 458.87 MB
2025-05-25 02:13:49,677 - app.utils.memory_management - INFO - Garbage collection: collected 1642 objects
2025-05-25 02:13:49,678 - app.utils.memory_management - INFO - Memory after cleanup: 458.87 MB (freed 0.00 MB)
2025-05-25 02:14:07,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:07,759 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:07,759 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:07,760 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:07,760 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:08,004 - app.utils.memory_management - INFO - Memory before cleanup: 458.99 MB
2025-05-25 02:14:08,120 - app.utils.memory_management - INFO - Garbage collection: collected 1643 objects
2025-05-25 02:14:08,120 - app.utils.memory_management - INFO - Memory after cleanup: 458.99 MB (freed 0.00 MB)
2025-05-25 02:14:09,074 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:09,097 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:09,098 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:09,098 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:09,098 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:09,407 - app.utils.memory_management - INFO - Memory before cleanup: 459.01 MB
2025-05-25 02:14:09,523 - app.utils.memory_management - INFO - Garbage collection: collected 2601 objects
2025-05-25 02:14:09,523 - app.utils.memory_management - INFO - Memory after cleanup: 459.01 MB (freed 0.00 MB)
2025-05-25 02:14:10,636 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:10,670 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:10,671 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:10,671 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:10,671 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:10,978 - app.utils.memory_management - INFO - Memory before cleanup: 459.71 MB
2025-05-25 02:14:11,092 - app.utils.memory_management - INFO - Garbage collection: collected 3372 objects
2025-05-25 02:14:11,093 - app.utils.memory_management - INFO - Memory after cleanup: 459.71 MB (freed 0.00 MB)
2025-05-25 02:14:15,254 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:14:15,282 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:14:15,283 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:14:15,283 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:14:15,284 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:14:15,610 - app.utils.memory_management - INFO - Memory before cleanup: 459.72 MB
2025-05-25 02:14:15,737 - app.utils.memory_management - INFO - Garbage collection: collected 2649 objects
2025-05-25 02:14:15,738 - app.utils.memory_management - INFO - Memory after cleanup: 459.72 MB (freed 0.00 MB)
2025-05-25 02:16:11,498 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:11,545 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:11,546 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:11,546 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:11,546 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:11,966 - app.utils.memory_management - INFO - Memory before cleanup: 459.69 MB
2025-05-25 02:16:12,071 - app.utils.memory_management - INFO - Garbage collection: collected 2163 objects
2025-05-25 02:16:12,071 - app.utils.memory_management - INFO - Memory after cleanup: 459.65 MB (freed 0.04 MB)
2025-05-25 02:16:13,672 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:13,697 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:13,698 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:13,698 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:13,698 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:14,010 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:14,154 - app.utils.memory_management - INFO - Garbage collection: collected 2165 objects
2025-05-25 02:16:14,154 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:18,117 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:18,142 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:18,143 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:18,143 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:18,143 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:18,458 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:18,558 - app.utils.memory_management - INFO - Garbage collection: collected 2135 objects
2025-05-25 02:16:18,558 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:19,823 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:19,851 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:16:19,852 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:19,852 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:19,852 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:20,160 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:20,282 - app.utils.memory_management - INFO - Garbage collection: collected 2167 objects
2025-05-25 02:16:20,282 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:21,898 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:21,931 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-25 02:16:21,932 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:21,932 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:21,933 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:22,255 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:22,362 - app.utils.memory_management - INFO - Garbage collection: collected 2167 objects
2025-05-25 02:16:22,362 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:16:22,846 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:16:22,872 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-25 02:16:22,873 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-25 02:16:22,873 - app.utils.common - INFO - Data shape: (572, 36)
2025-05-25 02:16:22,873 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-25 02:16:23,199 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-25 02:16:23,330 - app.utils.memory_management - INFO - Garbage collection: collected 2139 objects
2025-05-25 02:16:23,330 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-25 02:48:26,986 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 02:48:27,899 - app - INFO - Memory management utilities loaded
2025-05-25 02:48:27,900 - app - INFO - Error handling utilities loaded
2025-05-25 02:48:27,901 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 02:48:27,901 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 02:48:27,902 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 02:48:27,902 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 02:48:27,903 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 02:48:27,903 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 02:48:27,904 - app - INFO - Applied NumPy fix
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:48:27,904 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:48:27,904 - app - INFO - Applied NumPy BitGenerator fix
2025-05-25 02:48:30,507 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-25 02:48:30,507 - app - INFO - Applied TensorFlow fix
2025-05-25 02:48:30,507 - app.config - INFO - Configuration initialized
2025-05-25 02:48:30,507 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-25 02:48:30,528 - models.train - INFO - TensorFlow test successful
2025-05-25 02:48:30,851 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-25 02:48:30,851 - models.train - INFO - Transformer model is available
2025-05-25 02:48:30,851 - models.train - INFO - Using TensorFlow-based models
2025-05-25 02:48:30,852 - models.predict - INFO - Transformer model is available for predictions
2025-05-25 02:48:30,853 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-25 02:48:30,854 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-25 02:48:31,074 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:48:31,074 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-25 02:48:31,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 02:48:31,074 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 02:48:31,075 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 02:48:31,076 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 02:48:31,136 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-25 02:48:31,138 - app - INFO - Using TensorFlow-based LSTM model
2025-05-25 02:48:31,392 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-25 02:48:31,983 - app.services.llm_service - INFO - llama_cpp is available
