"""
AI Assistant Page - Consolidated Chat Assistant + LLM Insights + AI Services
"""

import streamlit as st
import logging
from typing import Optional, Dict, Any

# Import AI components
from app.components.ai_chat import ai_chat_component
from app.components.chat import chat_component
from app.components.enhanced_chat import EnhancedRuleBasedChat
from app.components.llm_insights_fix import llm_insights_component
from app.components.ai_services import ai_services_component

# Configure logging
logger = logging.getLogger(__name__)

def show_ai_assistant():
    """Main function to display the consolidated AI Assistant page"""
    
    st.title("🤖 AI Assistant")
    st.markdown("### Intelligent Analysis, Chat, and Insights for EGX Stocks")
    
    # Create tabs for different AI functionalities
    tab1, tab2, tab3, tab4 = st.tabs([
        "💬 Chat Assistant", 
        "🧠 LLM Insights", 
        "⚙️ AI Configuration",
        "📚 AI Guide"
    ])
    
    with tab1:
        show_chat_assistant()
    
    with tab2:
        show_llm_insights()
    
    with tab3:
        show_ai_configuration()
    
    with tab4:
        show_ai_guide()

def show_chat_assistant():
    """Chat assistant functionality"""
    
    st.header("💬 Chat Assistant")
    st.markdown("Ask questions about EGX stocks in natural language.")
    
    # Chat assistant capabilities info
    with st.expander("🎯 What can the Chat Assistant do?"):
        st.markdown("""
        **The AI Assistant can help you with:**
        
        🔸 **Current Stock Prices** - Get real-time prices for EGX stocks  
        🔸 **Price Predictions** - Forecast future stock prices  
        🔸 **Stock Comparisons** - Compare different stocks  
        🔸 **Market Information** - Get updates on the overall market  
        🔸 **Stock News** - Find the latest news about specific stocks  
        🔸 **Technical Analysis** - Explain charts and indicators  
        🔸 **Investment Advice** - Get insights on investment strategies  
        
        **Example questions:**
        - "What's the current price of COMI?"
        - "Predict HRHO for next week"
        - "Compare COMI and SWDY"
        - "How is the Egyptian market doing today?"
        - "Any news about COMI?"
        - "Explain the RSI indicator for ETEL"
        """)
    
    # Chat type selection
    st.subheader("🔧 Select Chat Assistant Type")
    
    chat_type = st.radio(
        "Choose your preferred chat assistant:",
        [
            "Enhanced Rule-based (Recommended)", 
            "AI-powered (OpenAI)", 
            "Basic Rule-based"
        ],
        index=0,
        key="ai_assistant_chat_type",
        help="Enhanced Rule-based works offline and provides intelligent responses without API keys"
    )
    
    # Initialize chat-specific session state
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    # Display the selected chat component
    st.markdown("---")
    
    try:
        if chat_type == "Enhanced Rule-based (Recommended)":
            st.info("🚀 Using Enhanced Rule-Based chat assistant - No API keys required!")
            # Initialize the enhanced rule-based chat system
            enhanced_chat = EnhancedRuleBasedChat()
            enhanced_chat.render_chat_interface()
            
        elif chat_type == "AI-powered (OpenAI)":
            st.info("🤖 Using AI-powered chat assistant - Requires OpenAI API key")
            ai_chat_component()
            
        else:  # Basic Rule-based
            st.info("📝 Using Basic Rule-based chat assistant")
            chat_component()
            
    except Exception as e:
        st.error(f"Error loading chat assistant: {str(e)}")
        logger.error(f"Chat assistant error: {str(e)}")
        
        # Fallback to basic chat
        st.warning("Falling back to basic chat assistant...")
        try:
            chat_component()
        except Exception as fallback_error:
            st.error(f"Fallback chat also failed: {str(fallback_error)}")
    
    # Chat management
    st.markdown("---")
    st.subheader("🗂️ Chat Management")
    
    chat_cols = st.columns(3)
    
    with chat_cols[0]:
        if st.button("🗑️ Clear Chat History", use_container_width=True):
            st.session_state.chat_history = []
            st.success("Chat history cleared!")
            st.rerun()
    
    with chat_cols[1]:
        if st.button("💾 Export Chat", use_container_width=True):
            if st.session_state.chat_history:
                chat_text = "\n".join([f"{msg['role']}: {msg['content']}" for msg in st.session_state.chat_history])
                st.download_button(
                    "📥 Download Chat History",
                    chat_text,
                    file_name="chat_history.txt",
                    mime="text/plain"
                )
            else:
                st.warning("No chat history to export")
    
    with chat_cols[2]:
        if st.button("🔄 Restart Chat", use_container_width=True):
            # Clear chat and reinitialize
            st.session_state.chat_history = []
            st.rerun()

def show_llm_insights():
    """LLM insights functionality"""
    
    st.header("🧠 LLM Insights")
    st.markdown("Advanced AI-powered analysis using local language models.")
    
    # LLM capabilities info
    with st.expander("✨ LLM Insights Features"):
        st.markdown("""
        **Local LLM-powered capabilities:**
        
        🔸 **Prediction Explanations** - Get natural language explanations of model predictions  
        🔸 **News Sentiment Analysis** - Analyze the sentiment of news related to stocks  
        🔸 **Investment Thesis Generation** - Generate comprehensive investment theses  
        🔸 **Financial Report Summarization** - Summarize financial reports in natural language  
        🔸 **Market Insights** - Create market insights dashboards  
        🔸 **Technical Analysis** - Explain technical indicators and patterns  
        
        **Privacy & Performance:**
        - ✅ Runs locally on your machine
        - ✅ No data sent to external servers
        - ✅ No API costs
        - ✅ Fast response times
        """)
    
    # Check if we have data for insights
    if st.session_state.historical_data is None or st.session_state.symbol is None:
        st.warning("⚠️ Please select a stock first to generate insights.")
        
        if st.button("📊 Select Stock", type="primary"):
            st.session_state.page = "Stock Management"
            st.rerun()
        return
    
    # Display current stock info
    st.info(f"📈 Generating insights for: **{st.session_state.symbol}**")
    
    # Use the LLM insights component
    try:
        llm_insights_component()
    except Exception as e:
        st.error(f"Error loading LLM insights: {str(e)}")
        logger.error(f"LLM insights error: {str(e)}")
        
        # Provide troubleshooting info
        st.markdown("### 🔧 Troubleshooting")
        st.markdown("""
        **If LLM Insights is not working:**
        
        1. **Check Model File**: Ensure the FinGPT model is in the correct location
        2. **Memory Requirements**: LLM requires sufficient RAM (8GB+ recommended)
        3. **Dependencies**: Verify llama-cpp-python is installed correctly
        4. **Alternative**: Use the Chat Assistant for similar functionality
        """)

def show_ai_configuration():
    """AI services configuration"""
    
    st.header("⚙️ AI Configuration")
    st.markdown("Configure AI services and model settings.")
    
    # Configuration sections
    config_tabs = st.tabs(["🔧 AI Services", "🤖 Local Models", "📊 Performance"])
    
    with config_tabs[0]:
        st.subheader("AI Services Integration")
        st.markdown("Configure external AI service providers for enhanced predictions.")
        
        # Use the AI services component
        try:
            ai_enabled, provider = ai_services_component()
            
            if ai_enabled:
                st.success(f"✅ AI services are enabled using {provider}")
                st.info("Your stock predictions will be enhanced with AI-powered insights!")
            else:
                st.info("AI services are currently disabled. Enable them to get enhanced predictions.")
                
        except Exception as e:
            st.error(f"Error in AI services configuration: {str(e)}")
            logger.error(f"AI services config error: {str(e)}")
    
    with config_tabs[1]:
        st.subheader("Local Model Configuration")
        
        # Model status
        st.markdown("**Local LLM Status:**")
        
        try:
            from app.services.llm_service import LLMService, LLAMA_CPP_AVAILABLE
            
            if LLAMA_CPP_AVAILABLE:
                st.success("✅ llama-cpp-python is available")
                
                # Check model file
                model_path = "D:/AI Stocks Bot/modelsllm/FinGPT-MT-Llama-3-8B-LoRA-Q4_K_M.gguf"
                import os
                if os.path.exists(model_path):
                    st.success("✅ FinGPT model file found")
                    
                    # Model info
                    file_size = os.path.getsize(model_path) / (1024**3)  # GB
                    st.info(f"📁 Model size: {file_size:.1f} GB")
                else:
                    st.error("❌ FinGPT model file not found")
                    st.markdown(f"**Expected location:** `{model_path}`")
            else:
                st.error("❌ llama-cpp-python is not available")
                st.markdown("**To install:** `pip install llama-cpp-python`")
                
        except Exception as e:
            st.error(f"Error checking local model status: {str(e)}")
        
        # Model settings
        st.markdown("**Model Settings:**")
        
        temperature = st.slider(
            "Temperature (creativity)",
            min_value=0.1,
            max_value=2.0,
            value=0.7,
            step=0.1,
            help="Higher values make output more creative but less focused"
        )
        
        max_tokens = st.slider(
            "Max tokens (response length)",
            min_value=100,
            max_value=2000,
            value=500,
            step=50,
            help="Maximum length of AI responses"
        )
        
        # Save settings
        if st.button("💾 Save Model Settings"):
            # Store in session state
            st.session_state.llm_temperature = temperature
            st.session_state.llm_max_tokens = max_tokens
            st.success("Settings saved!")
    
    with config_tabs[2]:
        st.subheader("Performance Monitoring")
        
        # AI performance metrics
        st.markdown("**AI Assistant Performance:**")
        
        # Mock performance data (replace with actual metrics)
        perf_cols = st.columns(3)
        
        with perf_cols[0]:
            st.metric("Response Time", "2.3s", delta="-0.5s")
        
        with perf_cols[1]:
            st.metric("Accuracy Score", "87%", delta="+3%")
        
        with perf_cols[2]:
            st.metric("User Satisfaction", "4.2/5", delta="+0.1")
        
        # Performance tips
        with st.expander("🚀 Performance Tips"):
            st.markdown("""
            **To improve AI performance:**
            
            - **Close other applications** to free up RAM for the LLM
            - **Use shorter prompts** for faster responses
            - **Lower temperature** for more focused answers
            - **Reduce max tokens** for quicker responses
            - **Restart the app** if responses become slow
            """)

def show_ai_guide():
    """AI assistant guide and tips"""
    
    st.header("📚 AI Assistant Guide")
    st.markdown("Learn how to get the most out of your AI assistant.")
    
    # Guide sections
    guide_sections = st.tabs(["🚀 Getting Started", "💡 Tips & Tricks", "❓ FAQ", "🔧 Troubleshooting"])
    
    with guide_sections[0]:
        st.subheader("🚀 Getting Started")
        
        st.markdown("""
        **Welcome to your AI Assistant! Here's how to get started:**
        
        ### 1. Choose Your Chat Type
        - **Enhanced Rule-based**: Best for most users, works offline
        - **AI-powered**: Requires OpenAI API key, more conversational
        - **Basic Rule-based**: Simple, fast responses
        
        ### 2. Ask Questions
        Start with simple questions like:
        - "What's the price of COMI?"
        - "Show me predictions for ETEL"
        - "Compare COMI and SWDY"
        
        ### 3. Use LLM Insights
        - Generate detailed analysis reports
        - Get prediction explanations
        - Analyze news sentiment
        
        ### 4. Configure Settings
        - Adjust model parameters
        - Set up AI services
        - Monitor performance
        """)
    
    with guide_sections[1]:
        st.subheader("💡 Tips & Tricks")
        
        st.markdown("""
        **Get better results with these tips:**
        
        ### 📝 Writing Better Prompts
        - **Be specific**: "Predict COMI for next 3 days" vs "Predict COMI"
        - **Include context**: "Given recent market volatility, what's your outlook on ETEL?"
        - **Ask follow-ups**: "Can you explain why you think the price will go up?"
        
        ### 🎯 Using Features Effectively
        - **Chat Assistant**: Great for quick questions and exploration
        - **LLM Insights**: Best for detailed analysis and reports
        - **Combine both**: Use chat to explore, then generate detailed insights
        
        ### ⚡ Performance Tips
        - **Keep conversations focused** on one stock at a time
        - **Clear chat history** regularly for better performance
        - **Use shorter questions** for faster responses
        """)
    
    with guide_sections[2]:
        st.subheader("❓ Frequently Asked Questions")
        
        faqs = {
            "How accurate are the AI predictions?": "AI predictions are estimates based on historical data and should not be used as sole investment advice. Always combine with your own research.",
            "Does the AI use real-time data?": "The AI can access your uploaded historical data and live price feeds when available. It doesn't have access to real-time news or market data beyond what you provide.",
            "Is my data secure?": "Yes! The local LLM runs entirely on your machine. No data is sent to external servers when using the Enhanced Rule-based chat.",
            "Why are responses sometimes slow?": "LLM processing requires significant computational resources. Response time depends on your hardware and the complexity of the question.",
            "Can I use this for actual trading?": "This tool is for educational and analysis purposes. Always consult with financial advisors before making investment decisions."
        }
        
        for question, answer in faqs.items():
            with st.expander(f"❓ {question}"):
                st.write(answer)
    
    with guide_sections[3]:
        st.subheader("🔧 Troubleshooting")
        
        st.markdown("""
        **Common issues and solutions:**
        
        ### 🚫 Chat Not Responding
        - **Check model status** in AI Configuration
        - **Restart the application**
        - **Try a different chat type**
        - **Clear chat history**
        
        ### 🐌 Slow Responses
        - **Close other applications** to free RAM
        - **Use shorter prompts**
        - **Lower temperature setting**
        - **Reduce max tokens**
        
        ### ❌ LLM Insights Not Working
        - **Verify model file location**
        - **Check available memory** (8GB+ recommended)
        - **Reinstall llama-cpp-python**
        - **Try restarting the app**
        
        ### 📊 No Predictions Available
        - **Train models first** in the Train Model page
        - **Select a stock** with historical data
        - **Check model training status**
        """)
        
        # Quick diagnostic
        st.markdown("### 🔍 Quick Diagnostic")
        
        if st.button("🔍 Run Diagnostic Check"):
            with st.spinner("Running diagnostic..."):
                # Check various components
                checks = {
                    "Historical Data": st.session_state.historical_data is not None,
                    "Stock Selected": st.session_state.symbol is not None,
                    "Chat History": 'chat_history' in st.session_state,
                }
                
                for check_name, status in checks.items():
                    if status:
                        st.success(f"✅ {check_name}: OK")
                    else:
                        st.error(f"❌ {check_name}: Issue detected")
                
                st.info("Diagnostic complete! Check the results above.")
