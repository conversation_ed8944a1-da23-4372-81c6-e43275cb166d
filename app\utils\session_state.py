"""
Session state management for the AI Stocks Bot application.

This module provides utilities for managing session state in the Streamlit application,
including initialization, persistence, and cleanup.
"""

import os
import sys
import logging
import json
import pickle
from datetime import datetime
from typing import Dict, Any, Optional, Union, List
import streamlit as st

# Configure logging
logger = logging.getLogger(__name__)

# Import error handling utilities
try:
    from app.utils.error_handling import handle_exception, log_exception
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    logger.warning("Error handling utilities not available in session_state module")
    ERROR_HANDLING_AVAILABLE = False

    # Define dummy functions if the real ones are not available
    def handle_exception(exc, default_return_value=None, raise_exception=False, log_level=logging.ERROR):
        logger.error(f"Error: {str(exc)}")
        if raise_exception:
            raise exc
        return default_return_value

    def log_exception(exc, level=logging.ERROR, include_traceback=True):
        logger.log(level, f"Exception: {str(exc)}")

# Import memory management utilities
try:
    from app.utils.memory_management import cleanup_memory
    MEMORY_MANAGEMENT_AVAILABLE = True
except ImportError:
    logger.warning("Memory management utilities not available in session_state module")
    MEMORY_MANAGEMENT_AVAILABLE = False

    # Define a dummy function if the real one is not available
    def cleanup_memory(objects_to_clean=None):
        pass

def initialize_session_state():
    """
    Initialize session state variables for the application.
    This should be called at the start of the Streamlit app.
    """
    # Define default session state variables if they don't exist
    if 'initialized' not in st.session_state:
        st.session_state.initialized = True
        logger.info("Initializing session state")
    else:
        logger.debug("Session state already initialized")
        return

    # Data-related state
    if 'historical_data' not in st.session_state:
        st.session_state.historical_data = None

    if 'symbol' not in st.session_state:
        st.session_state.symbol = None

    if 'live_data' not in st.session_state:
        st.session_state.live_data = None

    if 'last_update' not in st.session_state:
        st.session_state.last_update = None

    # Model-related state
    if 'predictions' not in st.session_state:
        st.session_state.predictions = {}

    if 'models' not in st.session_state:
        st.session_state.models = {}

    if 'model_metrics' not in st.session_state:
        st.session_state.model_metrics = {}

    # Configuration state
    if 'config' not in st.session_state:
        st.session_state.config = None

    # UI state
    if 'ui_settings' not in st.session_state:
        st.session_state.ui_settings = {
            'theme': 'light',
            'show_advanced_options': False,
            'sidebar_expanded': True
        }

    # Error tracking
    if 'errors' not in st.session_state:
        st.session_state.errors = []

    # Performance tracking
    if 'performance_metrics' not in st.session_state:
        st.session_state.performance_metrics = {
            'load_times': [],
            'prediction_times': [],
            'render_times': []
        }

    logger.info("Session state initialized")

def get_session_value(key: str, default: Any = None) -> Any:
    """
    Get a value from the session state.

    Args:
        key (str): The key to get from the session state.
        default (Any, optional): The default value to return if the key is not found.

    Returns:
        Any: The value from the session state, or the default if not found.
    """
    try:
        if key in st.session_state:
            return st.session_state[key]
        return default
    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=default)
        else:
            logger.error(f"Error getting session value {key}: {str(e)}")
            return default

def set_session_value(key: str, value: Any) -> None:
    """
    Set a value in the session state.

    Args:
        key (str): The key to set in the session state.
        value (Any): The value to set.
    """
    try:
        st.session_state[key] = value
    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error setting session value {key}: {str(e)}")

def clear_session_value(key: str) -> None:
    """
    Clear a value from the session state.

    Args:
        key (str): The key to clear from the session state.
    """
    try:
        if key in st.session_state:
            del st.session_state[key]
    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error clearing session value {key}: {str(e)}")

def save_session_state(file_path: str) -> bool:
    """
    Save the session state to a file.

    Args:
        file_path (str): The path to save the session state to.

    Returns:
        bool: True if the session state was saved successfully, False otherwise.
    """
    try:
        # Create a dictionary of serializable session state values
        state_dict = {}

        # These keys can be safely serialized
        serializable_keys = [
            'symbol',
            'last_update',
            'ui_settings',
            'errors'
        ]

        for key in serializable_keys:
            if key in st.session_state:
                state_dict[key] = st.session_state[key]

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

        # Save to file
        with open(file_path, 'w') as f:
            json.dump(state_dict, f, indent=2, default=str)

        logger.info(f"Saved session state to {file_path}")
        return True

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error saving session state: {str(e)}")
        return False

def load_session_state(file_path: str) -> bool:
    """
    Load the session state from a file.

    Args:
        file_path (str): The path to load the session state from.

    Returns:
        bool: True if the session state was loaded successfully, False otherwise.
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"Session state file not found: {file_path}")
            return False

        # Load from file
        with open(file_path, 'r') as f:
            state_dict = json.load(f)

        # Update session state
        for key, value in state_dict.items():
            st.session_state[key] = value

        logger.info(f"Loaded session state from {file_path}")
        return True

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error loading session state: {str(e)}")
        return False

def clear_session_state() -> None:
    """
    Clear the session state.
    """
    try:
        # Get keys to avoid modifying during iteration
        keys = list(st.session_state.keys())

        # Clear each key
        for key in keys:
            # Skip the initialized flag
            if key != 'initialized':
                clear_session_value(key)

        # Clean up memory
        if MEMORY_MANAGEMENT_AVAILABLE:
            cleanup_memory()

        logger.info("Session state cleared")

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error clearing session state: {str(e)}")

def track_error(error_message: str, error_type: str = "general", details: Optional[Dict[str, Any]] = None) -> None:
    """
    Track an error in the session state.

    Args:
        error_message (str): The error message.
        error_type (str, optional): The type of error.
        details (Dict[str, Any], optional): Additional details about the error.
    """
    try:
        if 'errors' not in st.session_state:
            st.session_state.errors = []

        error_entry = {
            'timestamp': datetime.now(),
            'message': str(error_message) if error_message is not None else "Unknown error",
            'type': str(error_type) if error_type is not None else "general"
        }

        if details:
            error_entry['details'] = details

        st.session_state.errors.append(error_entry)

        # Limit the number of errors stored
        if len(st.session_state.errors) > 100:
            st.session_state.errors = st.session_state.errors[-100:]

        # Ensure error_message and error_type are strings
        error_message_str = str(error_message) if error_message is not None else "Unknown error"
        error_type_str = str(error_type) if error_type is not None else "general"

        logger.error(f"Error tracked: {error_type_str} - {error_message_str}")

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error tracking error: {str(e)}")

def track_performance(category: str, duration: float) -> None:
    """
    Track performance metrics in the session state.

    Args:
        category (str): The category of performance metric (e.g., 'load', 'prediction', 'render').
        duration (float): The duration in seconds.
    """
    try:
        if 'performance_metrics' not in st.session_state:
            st.session_state.performance_metrics = {
                'load_times': [],
                'prediction_times': [],
                'render_times': []
            }

        category_key = f"{category}_times"

        if category_key not in st.session_state.performance_metrics:
            st.session_state.performance_metrics[category_key] = []

        metric_entry = {
            'timestamp': datetime.now(),
            'duration': duration
        }

        st.session_state.performance_metrics[category_key].append(metric_entry)

        # Limit the number of metrics stored
        if len(st.session_state.performance_metrics[category_key]) > 100:
            st.session_state.performance_metrics[category_key] = st.session_state.performance_metrics[category_key][-100:]

        logger.debug(f"Performance tracked: {category} - {duration:.4f}s")

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            log_exception(e)
        else:
            logger.error(f"Error tracking performance: {str(e)}")

def get_performance_summary() -> Dict[str, Dict[str, float]]:
    """
    Get a summary of performance metrics.

    Returns:
        Dict[str, Dict[str, float]]: A dictionary of performance metrics summaries.
    """
    try:
        if 'performance_metrics' not in st.session_state:
            return {}

        summary = {}

        for category, metrics in st.session_state.performance_metrics.items():
            if not metrics:
                continue

            durations = [m['duration'] for m in metrics]

            summary[category] = {
                'min': min(durations),
                'max': max(durations),
                'avg': sum(durations) / len(durations),
                'count': len(durations)
            }

        return summary

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value={})
        else:
            logger.error(f"Error getting performance summary: {str(e)}")
            return {}
