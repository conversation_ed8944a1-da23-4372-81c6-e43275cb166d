import numpy as np
import pandas as pd
import warnings
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, mean_absolute_error
import logging
import joblib
import os
from typing import Dict, Any, Tuple, List, Optional

# Configure logging first
logger = logging.getLogger(__name__)

# Try to import TensorFlow, but make it optional
TENSORFLOW_AVAILABLE = False
tf = None
Sequential = None
Model = None
Dense = None
LSTM = None
Dropout = None
Input = None
Concatenate = None
Adam = None

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, LSTM, Dropout, Input, Concatenate
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
    logger.info("TensorFlow imported successfully for Hybrid model")
except ImportError as e:
    warnings.warn(f"TensorFlow not available: {str(e)}. Hybrid model will use ML-only fallback mode.")
    logger.warning(f"TensorFlow import failed: {str(e)}. Hybrid model will use ML-only fallback mode.")
except Exception as e:
    warnings.warn(f"Error importing TensorFlow: {str(e)}. Hybrid model will use ML-only fallback mode.")
    logger.warning(f"TensorFlow import error: {str(e)}. Hybrid model will use ML-only fallback mode.")

from app.models.base_model import BaseModel
from app.utils.data_processor import create_sequences

class HybridModel(BaseModel):
    """
    Hybrid model that combines traditional ML with deep learning.

    This model uses a two-stage approach:
    1. Traditional ML models (RF, GB, SVR) for feature engineering
    2. LSTM deep learning for sequence modeling and final prediction
    """

    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the hybrid model.

        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional arguments for model configuration
        """
        super().__init__(target_column=target_column)
        self.model_name = "Hybrid"

        # Model parameters
        self.sequence_length = kwargs.get('sequence_length', 10)
        self.lstm_units = kwargs.get('lstm_units', 50)
        self.dense_units = kwargs.get('dense_units', 25)
        self.dropout_rate = kwargs.get('dropout_rate', 0.2)
        self.learning_rate = kwargs.get('learning_rate', 0.001)
        self.epochs = kwargs.get('epochs', 50)
        self.batch_size = kwargs.get('batch_size', 32)

        # Traditional ML models for feature engineering
        self.rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.svr_model = SVR(kernel='rbf')

        # Scalers
        self.feature_scaler = StandardScaler()
        self.target_scaler = MinMaxScaler()

        # LSTM model
        self.model = None

        # Feature importance
        self.feature_importance = {}

    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the hybrid model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Make a copy to avoid modifying the original data
        df = data.copy()

        # Extract target
        target = df[[self.target_column]]

        # Create features
        features = self._create_features(df)

        return features, target

    def _create_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Create features for the model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            pd.DataFrame: Engineered features
        """
        df = data.copy()

        # Basic price features
        price_cols = ['Open', 'High', 'Low', 'Close', 'Volume'] if 'Volume' in df.columns else ['Open', 'High', 'Low', 'Close']

        # Technical indicators (basic ones, will be expanded in technical indicators module)
        # Moving averages
        for ma_period in [5, 10, 20]:
            df[f'MA_{ma_period}'] = df['Close'].rolling(window=ma_period).mean()

        # Price momentum
        for period in [1, 5, 10]:
            df[f'Return_{period}d'] = df['Close'].pct_change(period)

        # Volatility
        for period in [5, 10, 20]:
            df[f'Volatility_{period}d'] = df['Close'].rolling(window=period).std()

        # High-Low range
        df['HL_Range'] = (df['High'] - df['Low']) / df['Close']

        # Fill NaN values
        df = df.fillna(method='bfill').fillna(method='ffill')

        return df

    def _generate_ml_features(self, X_train, y_train, X_test):
        """
        Generate features using traditional ML models.

        Args:
            X_train: Training features
            y_train: Training target
            X_test: Test features

        Returns:
            Tuple: ML-generated features for train and test sets
        """
        # Train ML models
        self.rf_model.fit(X_train, y_train.ravel())
        self.gb_model.fit(X_train, y_train.ravel())

        try:
            self.svr_model.fit(X_train, y_train.ravel())
            svr_trained = True
        except Exception as e:
            logger.warning(f"SVR training failed: {str(e)}. Skipping SVR features.")
            svr_trained = False

        # Generate predictions as features
        rf_train_pred = self.rf_model.predict(X_train).reshape(-1, 1)
        gb_train_pred = self.gb_model.predict(X_train).reshape(-1, 1)

        rf_test_pred = self.rf_model.predict(X_test).reshape(-1, 1)
        gb_test_pred = self.gb_model.predict(X_test).reshape(-1, 1)

        if svr_trained:
            svr_train_pred = self.svr_model.predict(X_train).reshape(-1, 1)
            svr_test_pred = self.svr_model.predict(X_test).reshape(-1, 1)

            # Combine ML features
            ml_features_train = np.hstack([rf_train_pred, gb_train_pred, svr_train_pred])
            ml_features_test = np.hstack([rf_test_pred, gb_test_pred, svr_test_pred])

            # Store feature importance
            self.feature_importance['RandomForest'] = self.rf_model.feature_importances_
            self.feature_importance['GradientBoosting'] = self.gb_model.feature_importances_
        else:
            # Combine ML features without SVR
            ml_features_train = np.hstack([rf_train_pred, gb_train_pred])
            ml_features_test = np.hstack([rf_test_pred, gb_test_pred])

            # Store feature importance
            self.feature_importance['RandomForest'] = self.rf_model.feature_importances_
            self.feature_importance['GradientBoosting'] = self.gb_model.feature_importances_

        return ml_features_train, ml_features_test

    def _build_model(self, input_shape, ml_features_shape):
        """
        Build the hybrid LSTM model.

        Args:
            input_shape: Shape of the time series input
            ml_features_shape: Shape of the ML-generated features

        Returns:
            tf.keras.Model: The hybrid model
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow not available. Cannot build Hybrid model.")
            return None

        try:
            # Time series input branch
            ts_input = Input(shape=input_shape, name='time_series_input')
            lstm1 = LSTM(self.lstm_units, return_sequences=True)(ts_input)
            dropout1 = Dropout(self.dropout_rate)(lstm1)
            lstm2 = LSTM(self.lstm_units)(dropout1)
            dropout2 = Dropout(self.dropout_rate)(lstm2)

            # ML features input branch
            ml_input = Input(shape=ml_features_shape, name='ml_features_input')
            ml_dense = Dense(self.dense_units, activation='relu')(ml_input)

            # Combine both branches
            combined = Concatenate()([dropout2, ml_dense])

            # Output layers
            dense1 = Dense(self.dense_units, activation='relu')(combined)
            output = Dense(1)(dense1)

            # Create model
            model = Model(inputs=[ts_input, ml_input], outputs=output)

            # Compile model
            model.compile(
                optimizer=Adam(learning_rate=self.learning_rate),
                loss='mse',
                metrics=['mae']
            )

            return model
        except Exception as e:
            logger.error(f"Error building Hybrid model: {str(e)}")
            return None

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the hybrid model.

        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters

        Returns:
            Dict[str, Any]: Training metrics
        """
        logger.info("Training Hybrid model")

        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available. Using ML-only fallback mode for Hybrid model.")
            # Train the ML models only as a fallback
            try:
                # Preprocess data
                features, target = self.preprocess_data(data)

                # Scale features and target
                X = self.feature_scaler.fit_transform(features)
                y = self.target_scaler.fit_transform(target)

                # Train ML models on full dataset
                self.rf_model.fit(X, y.ravel())
                self.gb_model.fit(X, y.ravel())
                try:
                    self.svr_model.fit(X, y.ravel())
                except Exception as e:
                    logger.warning(f"SVR training failed: {str(e)}. SVR will not be used for predictions.")

                # Store feature importance
                self.feature_importance['RandomForest'] = self.rf_model.feature_importances_
                self.feature_importance['GradientBoosting'] = self.gb_model.feature_importances_

                return {
                    'message': 'Trained ML-only fallback model successfully',
                    'feature_importance': self.feature_importance
                }
            except Exception as e:
                logger.error(f"Error training ML-only fallback model: {str(e)}")
                return {'error': str(e)}


        try:
            # Override default parameters if provided
            self.sequence_length = kwargs.get('sequence_length', self.sequence_length)
            self.lstm_units = kwargs.get('lstm_units', self.lstm_units)
            self.dense_units = kwargs.get('dense_units', self.dense_units)
            self.dropout_rate = kwargs.get('dropout_rate', self.dropout_rate)
            self.learning_rate = kwargs.get('learning_rate', self.learning_rate)
            self.epochs = kwargs.get('epochs', self.epochs)
            self.batch_size = kwargs.get('batch_size', self.batch_size)

            # Preprocess data
            features, target = self.preprocess_data(data)

            # Scale features and target
            X = self.feature_scaler.fit_transform(features)
            y = self.target_scaler.fit_transform(target)

            # Split data for validation
            train_size = int(len(X) * 0.8)
            X_train, X_val = X[:train_size], X[train_size:]
            y_train, y_val = y[:train_size], y[train_size:]

            # Generate ML features
            ml_features_train, ml_features_val = self._generate_ml_features(X_train, y_train, X_val)

            # Create sequences for LSTM
            X_train_seq, y_train_seq = create_sequences(X_train, y_train, self.sequence_length)
            X_val_seq, y_val_seq = create_sequences(X_val, y_val, self.sequence_length)

            # Adjust ML features to match sequence length
            ml_features_train_seq = ml_features_train[self.sequence_length:]
            ml_features_val_seq = ml_features_val[self.sequence_length:]

            # Build model
            self.model = self._build_model(
                input_shape=(X_train_seq.shape[1], X_train_seq.shape[2]),
                ml_features_shape=(ml_features_train_seq.shape[1],)
            )

            # Train model
            history = self.model.fit(
                [X_train_seq, ml_features_train_seq],
                y_train_seq,
                validation_data=([X_val_seq, ml_features_val_seq], y_val_seq),
                epochs=self.epochs,
                batch_size=self.batch_size,
                verbose=1,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(
                        monitor='val_loss',
                        patience=10,
                        restore_best_weights=True
                    )
                ]
            )

            # Generate ML features for full dataset for future predictions
            self.rf_model.fit(X, y.ravel())
            self.gb_model.fit(X, y.ravel())
            try:
                self.svr_model.fit(X, y.ravel())
            except Exception as e:
                logger.warning(f"SVR training on full dataset failed: {str(e)}. SVR will not be used for predictions.")

            # Return training metrics
            return {
                'history': history.history,
                'feature_importance': self.feature_importance
            }
        except Exception as e:
            logger.error(f"Error training Hybrid model: {str(e)}")
            return {'error': str(e)}

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the hybrid model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            np.ndarray: Predicted values
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available. Using ML-only fallback mode for predictions.")
            try:
                # Preprocess data
                features, _ = self.preprocess_data(data)

                # Scale features
                X = self.feature_scaler.transform(features)

                # Generate predictions from each ML model
                rf_pred = self.rf_model.predict(X).reshape(-1, 1)
                gb_pred = self.gb_model.predict(X).reshape(-1, 1)

                try:
                    svr_pred = self.svr_model.predict(X).reshape(-1, 1)
                    # Average the predictions from all models
                    ensemble_pred = np.mean(np.hstack([rf_pred, gb_pred, svr_pred]), axis=1)
                except Exception:
                    # Average without SVR if it fails
                    ensemble_pred = np.mean(np.hstack([rf_pred, gb_pred]), axis=1)

                # Reshape for inverse transform
                ensemble_pred = ensemble_pred.reshape(-1, 1)

                # Inverse transform predictions
                predictions = self.target_scaler.inverse_transform(ensemble_pred)

                return predictions.flatten()
            except Exception as e:
                logger.error(f"Error making predictions with ML-only fallback model: {str(e)}")
                return np.full(len(data), np.nan)

        if self.model is None:
            logger.error("Model has not been trained yet")
            return np.full(len(data), np.nan)

        try:
            # Preprocess data
            features, _ = self.preprocess_data(data)

            # Scale features
            X = self.feature_scaler.transform(features)

            # Generate ML features
            rf_pred = self.rf_model.predict(X).reshape(-1, 1)
            gb_pred = self.gb_model.predict(X).reshape(-1, 1)

            try:
                svr_pred = self.svr_model.predict(X).reshape(-1, 1)
                ml_features = np.hstack([rf_pred, gb_pred, svr_pred])
            except Exception:
                ml_features = np.hstack([rf_pred, gb_pred])

            # Create sequences for LSTM
            X_seq = []
            for i in range(len(X) - self.sequence_length + 1):
                X_seq.append(X[i:i+self.sequence_length])

            X_seq = np.array(X_seq)
            ml_features_seq = ml_features[self.sequence_length-1:]

            # Generate predictions
            predictions = self.model.predict([X_seq, ml_features_seq])

            # Inverse transform predictions
            predictions = self.target_scaler.inverse_transform(predictions)

            # Pad predictions to match input length
            padded_predictions = np.full(len(data), np.nan)
            padded_predictions[self.sequence_length-1:] = predictions.flatten()

            return padded_predictions
        except Exception as e:
            logger.error(f"Error making predictions with Hybrid model: {str(e)}")
            return np.full(len(data), np.nan)

    def save(self, path: str) -> None:
        """
        Save the hybrid model to disk.

        Args:
            path (str): The path to save the model
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available. Saving ML-only fallback model.")
            try:
                os.makedirs(os.path.dirname(path), exist_ok=True)

                # Save ML models and scalers
                model_data = {
                    'rf_model': self.rf_model,
                    'gb_model': self.gb_model,
                    'svr_model': self.svr_model,
                    'feature_scaler': self.feature_scaler,
                    'target_scaler': self.target_scaler,
                    'feature_importance': self.feature_importance,
                    'params': {
                        'target_column': self.target_column,
                        'is_fallback': True
                    }
                }

                joblib.dump(model_data, path)
                logger.info(f"ML-only fallback model saved to {path}")
                return
            except Exception as e:
                logger.error(f"Error saving ML-only fallback model: {str(e)}")
                return

        if self.model is None:
            logger.error("Model has not been trained yet. Cannot save.")
            return

        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)

            # Save LSTM model
            self.model.save(path + "_lstm")

            # Save ML models and scalers
            model_data = {
                'rf_model': self.rf_model,
                'gb_model': self.gb_model,
                'svr_model': self.svr_model,
                'feature_scaler': self.feature_scaler,
                'target_scaler': self.target_scaler,
                'feature_importance': self.feature_importance,
                'params': {
                    'sequence_length': self.sequence_length,
                    'lstm_units': self.lstm_units,
                    'dense_units': self.dense_units,
                    'dropout_rate': self.dropout_rate,
                    'learning_rate': self.learning_rate,
                    'target_column': self.target_column
                }
            }

            joblib.dump(model_data, path)
            logger.info(f"Hybrid model saved to {path}")
        except Exception as e:
            logger.error(f"Error saving Hybrid model: {str(e)}")

    def load(self, path: str) -> None:
        """
        Load the hybrid model from disk.

        Args:
            path (str): The path to load the model from
        """
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available. Loading ML-only fallback model.")
            try:
                # Load ML models and scalers
                model_data = joblib.load(path)

                self.rf_model = model_data['rf_model']
                self.gb_model = model_data['gb_model']
                self.svr_model = model_data['svr_model']
                self.feature_scaler = model_data['feature_scaler']
                self.target_scaler = model_data['target_scaler']
                self.feature_importance = model_data.get('feature_importance', {})

                # Load parameters
                params = model_data.get('params', {})
                self.target_column = params.get('target_column', self.target_column)

                logger.info(f"ML-only fallback model loaded from {path}")
                return
            except Exception as e:
                logger.error(f"Error loading ML-only fallback model: {str(e)}")
                return

        try:
            # Load LSTM model
            self.model = tf.keras.models.load_model(path + "_lstm")

            # Load ML models and scalers
            model_data = joblib.load(path)

            self.rf_model = model_data['rf_model']
            self.gb_model = model_data['gb_model']
            self.svr_model = model_data['svr_model']
            self.feature_scaler = model_data['feature_scaler']
            self.target_scaler = model_data['target_scaler']
            self.feature_importance = model_data.get('feature_importance', {})

            # Load parameters
            params = model_data.get('params', {})
            self.sequence_length = params.get('sequence_length', self.sequence_length)
            self.lstm_units = params.get('lstm_units', self.lstm_units)
            self.dense_units = params.get('dense_units', self.dense_units)
            self.dropout_rate = params.get('dropout_rate', self.dropout_rate)
            self.learning_rate = params.get('learning_rate', self.learning_rate)
            self.target_column = params.get('target_column', self.target_column)

            logger.info(f"Hybrid model loaded from {path}")
        except Exception as e:
            logger.error(f"Error loading Hybrid model: {str(e)}")

    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """
        Get feature importance from the traditional ML models.

        Returns:
            Dict[str, np.ndarray]: Feature importance for each model
        """
        return self.feature_importance
