"""
Fixed train method for ensemble models with all necessary imports.
"""

import os
import sys
import logging
from typing import Dict, Any, List, Tuple, Optional, Union
from datetime import datetime, timedelta

# Add the project root directory to the Python path if not already added
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Import model classes
from app.models.lstm_model import LSTMModel
from app.models.random_forest_model import RandomForestModel
from app.models.gradient_boosting_model import GradientBoostingModel
from app.models.svr_model import SVRModel
from app.models.linear_regression_model import LinearRegressionModel

# Configure logging
logger = logging.getLogger(__name__)

def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
    """
    Train all selected models and calculate optimal weights using the selected ensemble method.

    Args:
        data (pd.DataFrame): The training data
        **kwargs: Additional training parameters
            - val_split (float): Validation split ratio
            - test_split (float): Test split ratio
            - update_weights (bool): Whether to update weights based on new data

    Returns:
        Dict[str, Any]: Training metrics
    """
    # Initialize return values
    model_metrics = {}
    val_metrics = {}
    val_predictions = {}

    try:
        # Log the start of training
        logger.info(f"Training ensemble model with method: {self.ensemble_method}")
        logger.info(f"Selected models: {self.selected_models}")

        # Apply NumPy fixes to ensure stability
        try:
            from app.utils.numpy_fix_mt19937 import fix_numpy_mt19937
            fix_numpy_mt19937()
            logger.info("Applied NumPy MT19937 fix before ensemble training")
        except Exception as fix_error:
            logger.warning(f"Error applying NumPy MT19937 fix: {str(fix_error)}")

        try:
            from app.utils.numpy_bitgenerator_fix import fix_numpy_bitgenerator
            fix_numpy_bitgenerator()
            logger.info("Applied NumPy BitGenerator fix before ensemble training")
        except Exception as fix_error:
            logger.warning(f"Error applying NumPy BitGenerator fix: {str(fix_error)}")

        # Initialize models if not already done
        for model_name in self.selected_models:
            if model_name not in self.models:
                if model_name in self.default_models:
                    try:
                        # Create a fresh instance of the model
                        if model_name == 'LSTM':
                            self.models[model_name] = LSTMModel(target_column=self.target_column)
                        elif model_name == 'RandomForest':
                            self.models[model_name] = RandomForestModel(target_column=self.target_column)
                        elif model_name == 'GradientBoosting':
                            self.models[model_name] = GradientBoostingModel(target_column=self.target_column)
                        elif model_name == 'SVR':
                            self.models[model_name] = SVRModel(target_column=self.target_column)
                        elif model_name == 'LinearRegression':
                            self.models[model_name] = LinearRegressionModel(target_column=self.target_column)
                        else:
                            # Use the default model as fallback
                            self.models[model_name] = self.default_models[model_name]

                        logger.info(f"Initialized model: {model_name}")
                    except Exception as init_error:
                        logger.error(f"Error initializing model {model_name}: {str(init_error)}")
                        # Skip this model
                        continue
                else:
                    logger.warning(f"Model {model_name} not found in default models, skipping")

        # Check if we have any models to train
        if not self.models:
            logger.error("No models available for training")
            return {'error': 'No models available for training'}

        # Get validation split
        val_split = kwargs.get('val_split', 0.2)
        test_split = kwargs.get('test_split', 0.1)

        # Ensure we have enough data
        if len(data) < 10:  # Arbitrary minimum
            logger.error(f"Not enough data for training: {len(data)} rows")
            return {'error': 'Not enough data for training'}

        # Split data into train, validation, and test sets
        train_size = int(len(data) * (1 - val_split - test_split))
        val_size = int(len(data) * val_split)

        train_data = data.iloc[:train_size]
        val_data = data.iloc[train_size:train_size + val_size]
        test_data = data.iloc[train_size + val_size:]

        # Train each model
        for model_name, model in list(self.models.items()):  # Use list to allow dict modification during iteration
            try:
                logger.info(f"Training model: {model_name}")
                metrics = model.train(train_data)
                model_metrics[model_name] = metrics
                logger.info(f"Model {model_name} trained successfully")
            except Exception as e:
                logger.error(f"Error training model {model_name}: {str(e)}")
                # Remove the model from the ensemble
                self.models.pop(model_name, None)
                continue

        # Check if we have any successfully trained models
        if not model_metrics:
            logger.error("No models were successfully trained")
            return {'error': 'No models were successfully trained'}

        # Evaluate models on validation data
        try:
            X_val, y_val = self.preprocess_data(val_data)
        except Exception as e:
            logger.error(f"Error preprocessing validation data: {str(e)}")
            # Use a fallback approach
            y_val = val_data[self.target_column].values
            X_val = val_data.drop(columns=[self.target_column])

        # Evaluate each model
        for model_name, model in list(self.models.items()):  # Use list to allow dict modification
            try:
                # Get predictions for validation data
                val_pred = model.predict(val_data)
                val_predictions[model_name] = val_pred

                # Calculate metrics
                mse = mean_squared_error(y_val, val_pred)
                mae = mean_absolute_error(y_val, val_pred)

                # R2 can sometimes fail if predictions are constant
                try:
                    r2 = r2_score(y_val, val_pred)
                except:
                    r2 = 0.0

                val_metrics[model_name] = {
                    'MSE': mse,
                    'MAE': mae,
                    'R2': r2
                }

                # Store performance metrics for historical tracking
                timestamp = datetime.now()
                performance = {
                    'mse': mse,
                    'mae': mae,
                    'rmse': np.sqrt(mse),
                    'r2': r2,
                    'timestamp': timestamp
                }

                # Add to historical performances
                if not hasattr(self, 'historical_performances'):
                    self.historical_performances = {}

                if model_name not in self.historical_performances:
                    self.historical_performances[model_name] = []

                self.historical_performances[model_name].append(performance)

                # Keep only the most recent performances if we have a window defined
                if hasattr(self, 'performance_window') and self.performance_window > 0:
                    window_start = timestamp - timedelta(days=self.performance_window)
                    self.historical_performances[model_name] = [
                        perf for perf in self.historical_performances[model_name]
                        if perf['timestamp'] >= window_start
                    ]

                logger.info(f"Model {model_name} validation metrics: MSE={mse:.4f}, MAE={mae:.4f}, R2={r2:.4f}")
            except Exception as e:
                logger.error(f"Error evaluating model {model_name}: {str(e)}")
                # Continue with other models
                continue

        # Calculate weights based on the selected ensemble method
        try:
            if self.ensemble_method == 'simple_average':
                self._calculate_simple_average_weights()
            elif self.ensemble_method == 'weighted_average':
                self._calculate_weighted_average_weights()
            elif self.ensemble_method == 'performance_weighted':
                self._calculate_performance_weighted_weights()
            elif self.ensemble_method == 'time_weighted':
                self._calculate_time_weighted_weights()
            elif self.ensemble_method == 'stacking':
                self._train_stacking_model(val_data, val_predictions)
            else:
                # Default to simple average if method is not recognized
                logger.warning(f"Unrecognized ensemble method: {self.ensemble_method}, using simple average")
                self._calculate_simple_average_weights()
        except Exception as e:
            logger.error(f"Error calculating weights: {str(e)}")
            # Fall back to simple average
            self._calculate_simple_average_weights()

        logger.info(f"Ensemble model trained successfully with {len(self.models)} base models")
        logger.info(f"Weights: {self.weights}")

        # Return metrics from training
        return {
            'model_metrics': model_metrics,
            'validation_metrics': val_metrics,
            'model_performances': self.model_performances if hasattr(self, 'model_performances') else {},
            'weights': self.weights,
            'ensemble_method': self.ensemble_method if hasattr(self, 'ensemble_method') else 'simple_average'
        }
    except Exception as e:
        logger.error(f"Unexpected error in ensemble training: {str(e)}")
        return {'error': f"Unexpected error in ensemble training: {str(e)}"}
