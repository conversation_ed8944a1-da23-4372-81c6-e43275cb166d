2025-05-25 00:58:56,292 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-25 00:58:57,452 - app - INFO - Memory management utilities loaded
2025-05-25 00:58:57,452 - app - INFO - Error handling utilities loaded
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-25 00:58:57,452 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy fix
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-25 00:58:57,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-25 00:58:57,452 - app - INFO - Applied NumPy BitGenerator fix
