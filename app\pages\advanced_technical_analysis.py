"""
Advanced Technical Analysis Page

This page provides sophisticated technical analysis features including:
- AI-powered pattern recognition
- Volume profile analysis
- Market microstructure indicators
- Custom indicators
- Intermarket analysis
- Options flow analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import logging

# Import custom modules
from app.components.advanced_technical_analysis import AdvancedTechnicalAnalysis
from app.components.custom_indicators import CustomIndicators, IntermarketAnalysis, OptionsFlowAnalysis
from app.utils.common import load_stock_data
from app.utils.demo_data_generator import DemoDataGenerator

# Configure logging
logger = logging.getLogger(__name__)

def show_advanced_technical_analysis():
    """Main function to display the Advanced Technical Analysis page"""

    st.title("🔬 Advanced Technical Analysis")
    st.markdown("### AI-Powered Pattern Recognition & Market Microstructure Analysis")

    # Sidebar for controls
    with st.sidebar:
        st.header("Analysis Settings")

        # Stock selection
        stock_symbol = st.selectbox(
            "Select Stock",
            ["COMI", "ETEL", "SWDY", "HRHO", "EKHO"],
            index=0
        )

        # Time period
        period_days = st.selectbox(
            "Analysis Period",
            [30, 60, 90, 180, 365],
            index=2,
            help="Number of days for analysis"
        )

        # Analysis type
        analysis_type = st.multiselect(
            "Analysis Types",
            [
                "AI Pattern Recognition",
                "Volume Profile",
                "Market Microstructure",
                "Custom Indicators",
                "Intermarket Analysis",
                "Options Flow (Simulated)"
            ],
            default=["AI Pattern Recognition", "Volume Profile"]
        )

        # Demo mode option
        demo_mode = st.checkbox(
            "Demo Mode",
            value=False,
            help="Use generated demo data with realistic patterns for demonstration"
        )

        if demo_mode:
            pattern_type = st.selectbox(
                "Demo Pattern Type",
                ["realistic", "head_and_shoulders", "double_top", "triangle", "high_volume"],
                help="Type of pattern to demonstrate"
            )

    # Load data
    try:
        with st.spinner("Loading stock data..."):
            if demo_mode:
                # Generate demo data
                if pattern_type == "realistic":
                    data = DemoDataGenerator.generate_realistic_stock_data(
                        symbol=stock_symbol,
                        days=period_days,
                        start_price=80.0,
                        volatility=0.02
                    )
                elif pattern_type == "high_volume":
                    data = DemoDataGenerator.generate_high_volume_data()
                else:
                    data = DemoDataGenerator.generate_pattern_data(pattern_type)

                # Set date as index for demo data
                if 'Date' in data.columns:
                    data = data.set_index('Date')

                st.success(f"Generated {len(data)} days of demo data with {pattern_type} patterns")

            else:
                # Load real data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=period_days)

                data = load_stock_data(stock_symbol)

                # Filter data by date range if needed
                if data is not None and 'Date' in data.columns:
                    data['Date'] = pd.to_datetime(data['Date'])
                    data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
                    data = data.set_index('Date')

                if data is None or data.empty:
                    st.warning(f"No real data available for {stock_symbol}. Switching to demo mode...")
                    # Fallback to demo data
                    data = DemoDataGenerator.generate_realistic_stock_data(
                        symbol=stock_symbol,
                        days=period_days,
                        start_price=80.0,
                        volatility=0.02
                    )
                    if 'Date' in data.columns:
                        data = data.set_index('Date')
                    st.info(f"Using generated demo data with {len(data)} days")
                else:
                    st.success(f"Loaded {len(data)} days of real data for {stock_symbol}")

    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        # Fallback to demo data
        try:
            data = DemoDataGenerator.generate_realistic_stock_data(
                symbol=stock_symbol,
                days=period_days,
                start_price=80.0,
                volatility=0.02
            )
            if 'Date' in data.columns:
                data = data.set_index('Date')
            st.info(f"Using fallback demo data with {len(data)} days")
        except:
            st.error("Failed to generate demo data")
            return

    # Main analysis sections
    col1, col2 = st.columns([2, 1])

    with col1:
        # AI Pattern Recognition
        if "AI Pattern Recognition" in analysis_type:
            show_ai_pattern_recognition(data, stock_symbol)

        # Volume Profile
        if "Volume Profile" in analysis_type:
            show_volume_profile_analysis(data, stock_symbol)

        # Custom Indicators
        if "Custom Indicators" in analysis_type:
            show_custom_indicators(data, stock_symbol)

    with col2:
        # Market Microstructure
        if "Market Microstructure" in analysis_type:
            show_market_microstructure(data, stock_symbol)

        # Intermarket Analysis
        if "Intermarket Analysis" in analysis_type:
            show_intermarket_analysis(data, stock_symbol)

        # Options Flow
        if "Options Flow (Simulated)" in analysis_type:
            show_options_flow_analysis(data, stock_symbol)

def show_ai_pattern_recognition(data: pd.DataFrame, symbol: str):
    """Display AI-powered pattern recognition results"""

    st.subheader("🤖 AI Pattern Recognition")

    try:
        # Detect patterns
        with st.spinner("Detecting patterns with AI..."):
            patterns = AdvancedTechnicalAnalysis.detect_ai_patterns(data)

        if not patterns:
            st.info("No significant patterns detected in the current timeframe.")
            return

        # Display patterns
        st.write(f"**Found {len(patterns)} patterns:**")

        for i, pattern in enumerate(patterns):
            with st.expander(f"📊 {pattern['name']} - {pattern['confidence']:.1f}% confidence"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**Type:** {pattern['type'].title()}")
                    st.write(f"**Direction:** {pattern['direction'].title()}")
                    st.write(f"**Confidence:** {pattern['confidence']:.1f}%")

                with col2:
                    if 'target_price' in pattern:
                        st.write(f"**Target Price:** {pattern['target_price']:.2f}")
                    st.write(f"**Description:** {pattern['description']}")

        # Create pattern visualization
        fig = create_pattern_chart(data, patterns, symbol)
        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"Error in pattern recognition: {str(e)}")

def show_volume_profile_analysis(data: pd.DataFrame, symbol: str):
    """Display volume profile analysis"""

    st.subheader("📊 Volume Profile Analysis")

    try:
        # Calculate volume profile
        with st.spinner("Calculating volume profile..."):
            volume_profile = AdvancedTechnicalAnalysis.calculate_volume_profile(data)

        if 'error' in volume_profile:
            st.warning(f"Volume profile error: {volume_profile['error']}")
            return

        # Display key levels
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Point of Control", f"{volume_profile['poc_price']:.2f}")

        with col2:
            st.metric("Value Area High", f"{volume_profile['value_area_high']:.2f}")

        with col3:
            st.metric("Value Area Low", f"{volume_profile['value_area_low']:.2f}")

        # Create volume profile chart
        fig = create_volume_profile_chart(data, volume_profile, symbol)
        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"Error in volume profile analysis: {str(e)}")

def show_market_microstructure(data: pd.DataFrame, symbol: str):
    """Display market microstructure indicators"""

    st.subheader("🔬 Market Microstructure")

    try:
        # Calculate microstructure indicators
        indicators = AdvancedTechnicalAnalysis.calculate_market_microstructure_indicators(data)

        if 'error' in indicators:
            st.warning(f"Microstructure error: {indicators['error']}")
            return

        # Display indicators
        for name, value in indicators.items():
            if isinstance(value, (int, float)) and not np.isnan(value):
                st.metric(name.replace('_', ' '), f"{value:.4f}")

    except Exception as e:
        st.error(f"Error in microstructure analysis: {str(e)}")

def show_custom_indicators(data: pd.DataFrame, symbol: str):
    """Display custom indicators"""

    st.subheader("⚙️ Custom AI Indicators")

    try:
        # Calculate custom indicators
        with st.spinner("Calculating custom indicators..."):
            # Adaptive Moving Average
            ama = CustomIndicators.create_adaptive_moving_average(data['Close'])

            # AI Momentum
            ai_momentum = CustomIndicators.create_ai_momentum_indicator(data)

            # Volatility Bands
            vol_bands = CustomIndicators.create_volatility_bands(data['Close'])

        # Display current values
        col1, col2 = st.columns(2)

        with col1:
            st.metric("Adaptive MA", f"{ama.iloc[-1]:.2f}")
            st.metric("AI Momentum", f"{ai_momentum['AI_Momentum'].iloc[-1]:.4f}")

        with col2:
            st.metric("Band Position", f"{vol_bands['Band_Position'].iloc[-1]:.1f}%")
            st.metric("Band Width", f"{vol_bands['Band_Width'].iloc[-1]:.2f}%")

        # Create custom indicators chart
        fig = create_custom_indicators_chart(data, ama, ai_momentum, vol_bands, symbol)
        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"Error in custom indicators: {str(e)}")

def show_intermarket_analysis(data: pd.DataFrame, symbol: str):
    """Display intermarket analysis"""

    st.subheader("🌍 Intermarket Analysis")

    try:
        # Currency impact simulation
        currency_strength = st.slider("USD Strength", -1.0, 1.0, 0.0, 0.1)
        currency_impact = IntermarketAnalysis.simulate_currency_impact(data, currency_strength)

        if 'error' not in currency_impact:
            st.write("**Currency Impact:**")
            st.write(f"Price Impact: {currency_impact['price_impact_percent']:.2f}%")
            st.write(f"Recommendation: {currency_impact['recommendation']}")

        # Commodity impact simulation
        st.write("**Commodity Prices (% change):**")
        col1, col2, col3 = st.columns(3)

        with col1:
            oil_change = st.number_input("Oil", -10.0, 10.0, 0.0, 0.5)
        with col2:
            gold_change = st.number_input("Gold", -5.0, 5.0, 0.0, 0.5)
        with col3:
            copper_change = st.number_input("Copper", -10.0, 10.0, 0.0, 0.5)

        commodity_prices = {'oil': oil_change, 'gold': gold_change, 'copper': copper_change}
        commodity_impact = IntermarketAnalysis.simulate_commodity_impact(data, commodity_prices)

        if 'error' not in commodity_impact:
            st.write("**Commodity Impact:**")
            st.write(f"Total Impact: {commodity_impact['total_price_impact_percent']:.2f}%")
            st.write(f"Recommendation: {commodity_impact['recommendation']}")

    except Exception as e:
        st.error(f"Error in intermarket analysis: {str(e)}")

def show_options_flow_analysis(data: pd.DataFrame, symbol: str):
    """Display options flow analysis"""

    st.subheader("📈 Options Flow (Simulated)")

    try:
        # Calculate options flow
        options_flow = OptionsFlowAnalysis.simulate_options_flow(data)

        if 'error' not in options_flow:
            st.metric("Call/Put Ratio", f"{options_flow['call_put_ratio']:.2f}")
            st.write(f"**Sentiment:** {options_flow['sentiment']}")
            st.write(f"**Activity Level:** {options_flow['activity_level']}")
            st.write(f"**Recommendation:** {options_flow['recommendation']}")

    except Exception as e:
        st.error(f"Error in options flow analysis: {str(e)}")

def create_pattern_chart(data: pd.DataFrame, patterns: list, symbol: str):
    """Create chart with detected patterns"""

    fig = go.Figure()

    # Add candlestick chart
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['Open'],
        high=data['High'],
        low=data['Low'],
        close=data['Close'],
        name=symbol
    ))

    # Add pattern annotations
    for pattern in patterns:
        if 'start_idx' in pattern and 'end_idx' in pattern:
            start_idx = pattern['start_idx']
            end_idx = pattern['end_idx']

            # Add pattern annotation
            fig.add_annotation(
                x=data.index[end_idx],
                y=data['High'].iloc[end_idx],
                text=f"{pattern['name']}<br>{pattern['confidence']:.1f}%",
                showarrow=True,
                arrowhead=2,
                arrowcolor="red" if pattern['direction'] == 'bearish' else "green",
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="black"
            )

    fig.update_layout(
        title=f"{symbol} - AI Pattern Recognition",
        xaxis_title="Date",
        yaxis_title="Price",
        height=500
    )

    return fig

def create_volume_profile_chart(data: pd.DataFrame, volume_profile: dict, symbol: str):
    """Create volume profile chart"""

    fig = make_subplots(
        rows=1, cols=2,
        column_widths=[0.7, 0.3],
        subplot_titles=[f"{symbol} Price Chart", "Volume Profile"]
    )

    # Add price chart
    fig.add_trace(
        go.Candlestick(
            x=data.index,
            open=data['Open'],
            high=data['High'],
            low=data['Low'],
            close=data['Close'],
            name=symbol
        ),
        row=1, col=1
    )

    # Add volume profile
    fig.add_trace(
        go.Bar(
            y=volume_profile['price_bins'],
            x=volume_profile['volume_profile'],
            orientation='h',
            name="Volume Profile",
            marker_color='rgba(0,100,80,0.6)'
        ),
        row=1, col=2
    )

    # Add key levels
    poc_price = volume_profile['poc_price']
    va_high = volume_profile['value_area_high']
    va_low = volume_profile['value_area_low']

    # Add horizontal lines for key levels
    for col in [1, 2]:
        fig.add_hline(y=poc_price, line_dash="dash", line_color="red",
                     annotation_text="POC", row=1, col=col)
        fig.add_hline(y=va_high, line_dash="dot", line_color="blue",
                     annotation_text="VA High", row=1, col=col)
        fig.add_hline(y=va_low, line_dash="dot", line_color="blue",
                     annotation_text="VA Low", row=1, col=col)

    fig.update_layout(height=500, showlegend=False)

    return fig

def create_custom_indicators_chart(data: pd.DataFrame, ama: pd.Series, ai_momentum: dict, vol_bands: dict, symbol: str):
    """Create custom indicators chart"""

    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=[f"{symbol} with Custom Indicators", "AI Momentum", "Volatility Bands"],
        vertical_spacing=0.1
    )

    # Price with AMA
    fig.add_trace(
        go.Scatter(x=data.index, y=data['Close'], name="Close", line=dict(color='black')),
        row=1, col=1
    )
    fig.add_trace(
        go.Scatter(x=data.index, y=ama, name="Adaptive MA", line=dict(color='blue')),
        row=1, col=1
    )

    # AI Momentum
    fig.add_trace(
        go.Scatter(x=data.index, y=ai_momentum['AI_Momentum'], name="AI Momentum", line=dict(color='purple')),
        row=2, col=1
    )

    # Volatility Bands
    fig.add_trace(
        go.Scatter(x=data.index, y=vol_bands['Upper_Band'], name="Upper Band", line=dict(color='red', dash='dash')),
        row=3, col=1
    )
    fig.add_trace(
        go.Scatter(x=data.index, y=vol_bands['Middle_Band'], name="Middle Band", line=dict(color='blue')),
        row=3, col=1
    )
    fig.add_trace(
        go.Scatter(x=data.index, y=vol_bands['Lower_Band'], name="Lower Band", line=dict(color='green', dash='dash')),
        row=3, col=1
    )

    fig.update_layout(height=800, showlegend=False)

    return fig

if __name__ == "__main__":
    show_advanced_technical_analysis()
