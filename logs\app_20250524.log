2025-05-24 16:08:02,192 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-24 16:08:08,133 - app - INFO - Memory management utilities loaded
2025-05-24 16:08:08,157 - app - INFO - Error handling utilities loaded
2025-05-24 16:08:08,167 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-24 16:08:08,169 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-24 16:08:08,170 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-24 16:08:08,170 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-24 16:08:08,184 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-24 16:08:08,185 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-24 16:08:08,186 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-24 16:08:08,186 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-24 16:08:08,187 - app - INFO - Applied NumPy fix
2025-05-24 16:08:08,194 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:08:08,194 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:08:08,195 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:08:08,195 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-24 16:08:08,195 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:08:08,196 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:08:08,196 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:08:08,196 - app - INFO - Applied NumPy BitGenerator fix
2025-05-24 16:08:21,826 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-24 16:08:21,826 - app - INFO - Applied TensorFlow fix
2025-05-24 16:08:21,846 - app.config - INFO - Configuration initialized
2025-05-24 16:08:21,863 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-24 16:08:22,237 - models.train - INFO - TensorFlow test successful
2025-05-24 16:08:26,455 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-24 16:08:26,456 - models.train - INFO - Transformer model is available
2025-05-24 16:08:26,456 - models.train - INFO - Using TensorFlow-based models
2025-05-24 16:08:26,463 - models.predict - INFO - Transformer model is available for predictions
2025-05-24 16:08:26,464 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-24 16:08:26,959 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:08:26,959 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:08:26,960 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:08:26,960 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:08:26,960 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:08:26,960 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-24 16:08:26,960 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-24 16:08:26,961 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:08:26,961 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:08:26,961 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:08:27,273 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:08:28,040 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-24 16:08:29,865 - app.services.llm_service - INFO - llama_cpp is available
2025-05-24 16:08:29,906 - app.utils.session_state - INFO - Initializing session state
2025-05-24 16:08:29,907 - app.utils.session_state - INFO - Session state initialized
2025-05-24 16:08:30,988 - app - INFO - Found 8 stock files in data/stocks
2025-05-24 16:08:30,995 - app.utils.memory_management - INFO - Memory before cleanup: 427.28 MB
2025-05-24 16:08:31,150 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-24 16:08:31,150 - app.utils.memory_management - INFO - Memory after cleanup: 427.29 MB (freed -0.02 MB)
2025-05-24 16:08:35,763 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:08:35,794 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-24 16:08:35,935 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.10 seconds
2025-05-24 16:08:35,948 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-24 16:08:35,949 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-24 16:08:35,952 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-24 16:08:37,373 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 1.58 seconds
2025-05-24 16:08:37,390 - app.utils.memory_management - INFO - Memory before cleanup: 438.91 MB
2025-05-24 16:08:37,519 - app.utils.memory_management - INFO - Garbage collection: collected 1615 objects
2025-05-24 16:08:37,519 - app.utils.memory_management - INFO - Memory after cleanup: 438.94 MB (freed -0.03 MB)
2025-05-24 16:08:56,875 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:08:56,879 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-24 16:08:56,901 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-24 16:08:56,902 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-24 16:08:56,902 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-24 16:08:56,902 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-24 16:08:57,117 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.24 seconds
2025-05-24 16:08:57,121 - app.utils.memory_management - INFO - Memory before cleanup: 441.29 MB
2025-05-24 16:08:57,273 - app.utils.memory_management - INFO - Garbage collection: collected 1446 objects
2025-05-24 16:08:57,276 - app.utils.memory_management - INFO - Memory after cleanup: 441.33 MB (freed -0.04 MB)
2025-05-24 16:08:58,293 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:08:58,295 - app.utils.memory_management - INFO - Memory before cleanup: 441.33 MB
2025-05-24 16:08:58,446 - app.utils.memory_management - INFO - Garbage collection: collected 218 objects
2025-05-24 16:08:58,446 - app.utils.memory_management - INFO - Memory after cleanup: 441.33 MB (freed 0.00 MB)
2025-05-24 16:09:03,741 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:03,758 - app.utils.memory_management - INFO - Memory before cleanup: 441.33 MB
2025-05-24 16:09:03,882 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-05-24 16:09:03,882 - app.utils.memory_management - INFO - Memory after cleanup: 441.33 MB (freed 0.00 MB)
2025-05-24 16:09:05,100 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:05,158 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-05-24 16:09:05,159 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:09:05,160 - app - INFO - Data shape: (572, 36)
2025-05-24 16:09:05,160 - app - INFO - File COMI contains 2025 data
2025-05-24 16:09:05,187 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-24 16:09:05,187 - app - INFO - Features shape: (572, 36)
2025-05-24 16:09:05,203 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-24 16:09:05,203 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:09:05,203 - app - INFO - Data shape: (572, 36)
2025-05-24 16:09:05,203 - app - INFO - File COMI contains 2025 data
2025-05-24 16:09:05,224 - app.utils.memory_management - INFO - Memory before cleanup: 443.32 MB
2025-05-24 16:09:05,333 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-05-24 16:09:05,349 - app.utils.memory_management - INFO - Memory after cleanup: 443.32 MB (freed -0.00 MB)
2025-05-24 16:09:12,578 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:12,877 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-24 16:09:13,424 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-24 16:09:13,424 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-24 16:09:13,424 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-24 16:09:13,424 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-24 16:09:13,424 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-24 16:09:13,624 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-24 16:09:14,010 - app.utils.memory_management - INFO - Memory before cleanup: 444.48 MB
2025-05-24 16:09:14,153 - app.utils.memory_management - INFO - Garbage collection: collected 3012 objects
2025-05-24 16:09:14,153 - app.utils.memory_management - INFO - Memory after cleanup: 444.46 MB (freed 0.02 MB)
2025-05-24 16:09:28,378 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:28,652 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-24 16:09:28,879 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-24 16:09:28,889 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-24 16:09:28,890 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-24 16:09:28,891 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-24 16:09:28,892 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-24 16:09:29,094 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-24 16:09:29,320 - app.utils.memory_management - INFO - Memory before cleanup: 443.12 MB
2025-05-24 16:09:29,459 - app.utils.memory_management - INFO - Garbage collection: collected 2809 objects
2025-05-24 16:09:29,460 - app.utils.memory_management - INFO - Memory after cleanup: 443.12 MB (freed 0.00 MB)
2025-05-24 16:09:29,604 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:29,637 - app.utils.memory_management - INFO - Memory before cleanup: 443.13 MB
2025-05-24 16:09:29,782 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-24 16:09:29,783 - app.utils.memory_management - INFO - Memory after cleanup: 443.13 MB (freed 0.00 MB)
2025-05-24 16:09:32,092 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:32,115 - app.utils.memory_management - INFO - Memory before cleanup: 443.14 MB
2025-05-24 16:09:32,252 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-05-24 16:09:32,253 - app.utils.memory_management - INFO - Memory after cleanup: 443.14 MB (freed 0.00 MB)
2025-05-24 16:09:33,804 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:33,829 - app.utils.memory_management - INFO - Memory before cleanup: 443.15 MB
2025-05-24 16:09:33,953 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-05-24 16:09:33,954 - app.utils.memory_management - INFO - Memory after cleanup: 443.15 MB (freed 0.00 MB)
2025-05-24 16:09:35,369 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:35,394 - app.utils.memory_management - INFO - Memory before cleanup: 443.16 MB
2025-05-24 16:09:35,512 - app.utils.memory_management - INFO - Garbage collection: collected 192 objects
2025-05-24 16:09:35,512 - app.utils.memory_management - INFO - Memory after cleanup: 443.16 MB (freed 0.00 MB)
2025-05-24 16:09:36,271 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:36,396 - app.utils.memory_management - INFO - Memory before cleanup: 444.62 MB
2025-05-24 16:09:36,520 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-05-24 16:09:36,520 - app.utils.memory_management - INFO - Memory after cleanup: 444.62 MB (freed 0.00 MB)
2025-05-24 16:09:37,918 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:37,992 - app.utils.memory_management - INFO - Memory before cleanup: 444.70 MB
2025-05-24 16:09:38,116 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-05-24 16:09:38,117 - app.utils.memory_management - INFO - Memory after cleanup: 444.70 MB (freed 0.00 MB)
2025-05-24 16:09:38,707 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:38,723 - app.utils.memory_management - INFO - Memory before cleanup: 444.71 MB
2025-05-24 16:09:38,832 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-24 16:09:38,849 - app.utils.memory_management - INFO - Memory after cleanup: 444.71 MB (freed 0.00 MB)
2025-05-24 16:09:41,151 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:41,168 - app.utils.memory_management - INFO - Memory before cleanup: 444.72 MB
2025-05-24 16:09:41,301 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-05-24 16:09:41,301 - app.utils.memory_management - INFO - Memory after cleanup: 444.72 MB (freed 0.00 MB)
2025-05-24 16:09:42,363 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:09:42,575 - app.utils.memory_management - INFO - Memory before cleanup: 446.51 MB
2025-05-24 16:09:42,689 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-05-24 16:09:42,689 - app.utils.memory_management - INFO - Memory after cleanup: 446.51 MB (freed 0.00 MB)
2025-05-24 16:23:33,696 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-24 16:23:33,696 - app - INFO - Memory management utilities loaded
2025-05-24 16:23:33,696 - app - INFO - Error handling utilities loaded
2025-05-24 16:23:33,705 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-24 16:23:33,706 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-24 16:23:33,707 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-24 16:23:33,707 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-24 16:24:42,352 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-24 16:24:43,274 - app - INFO - Memory management utilities loaded
2025-05-24 16:24:43,274 - app - INFO - Error handling utilities loaded
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-24 16:24:43,274 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-24 16:24:43,274 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-24 16:24:43,274 - app - INFO - Applied NumPy fix
2025-05-24 16:24:43,283 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:24:43,283 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:24:43,283 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:24:43,283 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-24 16:24:43,283 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:24:43,284 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:24:43,284 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:24:43,284 - app - INFO - Applied NumPy BitGenerator fix
2025-05-24 16:24:46,080 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-24 16:24:46,081 - app - INFO - Applied TensorFlow fix
2025-05-24 16:24:46,082 - app.config - INFO - Configuration initialized
2025-05-24 16:24:46,085 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-24 16:24:46,091 - models.train - INFO - TensorFlow test successful
2025-05-24 16:24:46,413 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-24 16:24:46,413 - models.train - INFO - Transformer model is available
2025-05-24 16:24:46,413 - models.train - INFO - Using TensorFlow-based models
2025-05-24 16:24:46,414 - models.predict - INFO - Transformer model is available for predictions
2025-05-24 16:24:46,415 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-24 16:24:46,418 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-24 16:24:46,626 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:24:46,626 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:24:46,626 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:24:46,627 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:24:46,627 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:24:46,627 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-24 16:24:46,627 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-24 16:24:46,627 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:24:46,628 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:24:46,628 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:24:46,685 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-24 16:24:46,687 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:24:46,942 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-24 16:24:47,328 - app.services.llm_service - INFO - llama_cpp is available
2025-05-24 16:24:47,334 - app.utils.session_state - INFO - Initializing session state
2025-05-24 16:24:47,336 - app.utils.session_state - INFO - Session state initialized
2025-05-24 16:24:48,294 - app - INFO - Found 8 stock files in data/stocks
2025-05-24 16:24:48,299 - app.utils.memory_management - INFO - Memory before cleanup: 427.88 MB
2025-05-24 16:24:48,408 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-24 16:24:48,408 - app.utils.memory_management - INFO - Memory after cleanup: 428.22 MB (freed -0.35 MB)
2025-05-24 16:24:54,907 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:24:54,920 - app.utils.memory_management - INFO - Memory before cleanup: 431.16 MB
2025-05-24 16:24:55,031 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-24 16:24:55,031 - app.utils.memory_management - INFO - Memory after cleanup: 431.16 MB (freed 0.00 MB)
2025-05-24 16:24:56,068 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:24:56,103 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-24 16:24:56,103 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:24:56,104 - app - INFO - Data shape: (572, 36)
2025-05-24 16:24:56,104 - app - INFO - File COMI contains 2025 data
2025-05-24 16:24:56,123 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-24 16:24:56,123 - app - INFO - Features shape: (572, 36)
2025-05-24 16:24:56,141 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-24 16:24:56,142 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:24:56,142 - app - INFO - Data shape: (572, 36)
2025-05-24 16:24:56,142 - app - INFO - File COMI contains 2025 data
2025-05-24 16:24:56,165 - app.utils.memory_management - INFO - Memory before cleanup: 436.21 MB
2025-05-24 16:24:56,282 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-24 16:24:56,282 - app.utils.memory_management - INFO - Memory after cleanup: 436.25 MB (freed -0.04 MB)
2025-05-24 16:25:00,635 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:00,862 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-24 16:25:01,259 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-24 16:25:01,268 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-24 16:25:01,269 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-24 16:25:01,269 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-24 16:25:01,271 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-24 16:25:01,543 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.28 seconds
2025-05-24 16:25:01,766 - app.utils.memory_management - INFO - Memory before cleanup: 447.57 MB
2025-05-24 16:25:01,884 - app.utils.memory_management - INFO - Garbage collection: collected 3875 objects
2025-05-24 16:25:01,885 - app.utils.memory_management - INFO - Memory after cleanup: 447.57 MB (freed 0.00 MB)
2025-05-24 16:25:15,858 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:15,893 - app.utils.memory_management - INFO - Memory before cleanup: 447.07 MB
2025-05-24 16:25:16,010 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-24 16:25:16,010 - app.utils.memory_management - INFO - Memory after cleanup: 447.07 MB (freed 0.00 MB)
2025-05-24 16:25:20,970 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:21,039 - app.utils.memory_management - INFO - Memory before cleanup: 448.10 MB
2025-05-24 16:25:21,176 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-24 16:25:21,176 - app.utils.memory_management - INFO - Memory after cleanup: 448.10 MB (freed 0.00 MB)
2025-05-24 16:25:24,277 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:24,348 - app.utils.memory_management - INFO - Memory before cleanup: 449.00 MB
2025-05-24 16:25:24,515 - app.utils.memory_management - INFO - Garbage collection: collected 278 objects
2025-05-24 16:25:24,515 - app.utils.memory_management - INFO - Memory after cleanup: 449.00 MB (freed 0.00 MB)
2025-05-24 16:25:25,612 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:25,636 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-24 16:25:25,646 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-24 16:25:25,647 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-24 16:25:25,654 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-24 16:25:25,658 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-24 16:25:25,659 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-24 16:25:25,661 - app.utils.memory_management - INFO - Memory before cleanup: 449.00 MB
2025-05-24 16:25:25,774 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-24 16:25:25,775 - app.utils.memory_management - INFO - Memory after cleanup: 449.00 MB (freed 0.00 MB)
2025-05-24 16:25:33,806 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:25:33,948 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-24 16:25:35,138 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-24 16:25:35,138 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-24 16:25:35,145 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.95
2025-05-24 16:25:35,199 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-24 16:25:35,200 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-24 16:25:35,200 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-24 16:25:41,082 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-24 16:25:43,211 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-24 16:25:43,219 - scrapers.price_scraper - INFO - Successfully extracted price from header: 79.9
2025-05-24 16:25:43,219 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 79.9
2025-05-24 16:25:43,219 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-24 16:25:43,219 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-24 16:25:43,219 - app.utils.error_handling - INFO - fetch_price executed in 8.02 seconds
2025-05-24 16:25:43,220 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-24 16:25:45,365 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-05-24 16:25:45,366 - app.models.adaptive - INFO - No valid models for COMI with 4min horizon, using equal weights
2025-05-24 16:25:45,367 - app.models.predict - INFO - Ensemble weights for COMI with 4min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-24 16:25:45,436 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-24 16:25:45,549 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-24 16:25:45,665 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-24 16:25:45,666 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-24 16:25:45,691 - models.hybrid_model - INFO - XGBoost is available
2025-05-24 16:25:45,692 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-24 16:25:45,692 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-24 16:25:45,693 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-24 16:25:45,693 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-24 16:25:45,762 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-24 16:25:45,762 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-24 16:25:45,762 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-24 16:25:45,767 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-24 16:25:45,767 - models.predict - INFO - Current price: 80.68, Predicted scaled value: 0.7069471073150635
2025-05-24 16:25:45,768 - models.predict - INFO - Prediction for 4 minutes horizon: 80.44069977538848
2025-05-24 16:25:45,819 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-24 16:25:45,926 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-24 16:25:46,037 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-24 16:25:46,038 - models.predict - INFO - Using scikit-learn gb model for 4 minutes horizon
2025-05-24 16:25:46,039 - models.predict - INFO - Loading gb model for COMI with horizon 4
2025-05-24 16:25:46,039 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4min.joblib
2025-05-24 16:25:46,039 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4min.joblib
2025-05-24 16:25:46,041 - models.predict - ERROR - Model file not found or import error: No module named '_loss'
2025-05-24 16:25:46,041 - models.predict - WARNING - Attempting to load scikit-learn model with explicit joblib path
2025-05-24 16:25:46,042 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4min.joblib
2025-05-24 16:25:46,042 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4min.joblib
2025-05-24 16:25:46,043 - models.predict - ERROR - Failed to load model with explicit joblib path: No module named '_loss'
2025-05-24 16:25:46,044 - models.predict - WARNING - Attempting to apply numpy fix directly
2025-05-24 16:25:46,044 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-24 16:25:46,044 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-24 16:25:46,045 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-24 16:25:46,045 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-24 16:29:57,023 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-24 16:29:58,025 - app - INFO - Memory management utilities loaded
2025-05-24 16:29:58,041 - app - INFO - Error handling utilities loaded
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-24 16:29:58,041 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-24 16:29:58,041 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-24 16:29:58,041 - app - INFO - Applied NumPy fix
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:29:58,041 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:29:58,041 - app - INFO - Applied NumPy BitGenerator fix
2025-05-24 16:30:00,672 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-24 16:30:00,672 - app - INFO - Applied TensorFlow fix
2025-05-24 16:30:00,674 - app.config - INFO - Configuration initialized
2025-05-24 16:30:00,676 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-24 16:30:00,678 - models.train - INFO - TensorFlow test successful
2025-05-24 16:30:01,015 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-24 16:30:01,015 - models.train - INFO - Transformer model is available
2025-05-24 16:30:01,015 - models.train - INFO - Using TensorFlow-based models
2025-05-24 16:30:01,021 - models.predict - INFO - Transformer model is available for predictions
2025-05-24 16:30:01,021 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-24 16:30:01,023 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-24 16:30:01,237 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:30:01,238 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-24 16:30:01,238 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-24 16:30:01,238 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-24 16:30:01,238 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-24 16:30:01,238 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-24 16:30:01,239 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-24 16:30:01,239 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-24 16:30:01,239 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-24 16:30:01,239 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-24 16:30:01,295 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-24 16:30:01,295 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:01,536 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-24 16:30:01,903 - app.services.llm_service - INFO - llama_cpp is available
2025-05-24 16:30:01,910 - app.utils.session_state - INFO - Initializing session state
2025-05-24 16:30:01,912 - app.utils.session_state - INFO - Session state initialized
2025-05-24 16:30:02,891 - app - INFO - Found 8 stock files in data/stocks
2025-05-24 16:30:02,896 - app.utils.memory_management - INFO - Memory before cleanup: 426.87 MB
2025-05-24 16:30:03,002 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-24 16:30:03,002 - app.utils.memory_management - INFO - Memory after cleanup: 426.88 MB (freed -0.01 MB)
2025-05-24 16:30:08,543 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:08,556 - app.utils.memory_management - INFO - Memory before cleanup: 430.38 MB
2025-05-24 16:30:08,669 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-24 16:30:08,669 - app.utils.memory_management - INFO - Memory after cleanup: 430.38 MB (freed 0.00 MB)
2025-05-24 16:30:09,804 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:09,840 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-24 16:30:09,841 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:30:09,841 - app - INFO - Data shape: (572, 36)
2025-05-24 16:30:09,842 - app - INFO - File COMI contains 2025 data
2025-05-24 16:30:09,861 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-24 16:30:09,862 - app - INFO - Features shape: (572, 36)
2025-05-24 16:30:09,876 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-24 16:30:09,876 - app - INFO - Date range: 2023-01-02 to 2025-05-18
2025-05-24 16:30:09,876 - app - INFO - Data shape: (572, 36)
2025-05-24 16:30:09,876 - app - INFO - File COMI contains 2025 data
2025-05-24 16:30:09,898 - app.utils.memory_management - INFO - Memory before cleanup: 435.95 MB
2025-05-24 16:30:10,011 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-24 16:30:10,012 - app.utils.memory_management - INFO - Memory after cleanup: 436.00 MB (freed -0.05 MB)
2025-05-24 16:30:13,274 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:13,306 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-24 16:30:13,312 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-24 16:30:13,312 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-24 16:30:13,325 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-24 16:30:13,331 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-24 16:30:13,331 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-24 16:30:13,334 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-05-24 16:30:13,459 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-05-24 16:30:13,459 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
2025-05-24 16:30:21,111 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:21,137 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-24 16:30:21,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-24 16:30:21,154 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-24 16:30:21,158 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-24 16:30:21,163 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-24 16:30:21,165 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-24 16:30:21,168 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-05-24 16:30:21,282 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-24 16:30:21,282 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
2025-05-24 16:30:22,041 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:22,065 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-24 16:30:22,078 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-24 16:30:22,078 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-24 16:30:22,085 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-24 16:30:22,090 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-24 16:30:22,091 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-24 16:30:22,093 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-05-24 16:30:22,205 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-24 16:30:22,205 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
2025-05-24 16:30:24,657 - app - INFO - Using TensorFlow-based LSTM model
2025-05-24 16:30:24,683 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-24 16:30:24,692 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-24 16:30:24,693 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-24 16:30:24,693 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-24 16:30:24,702 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-24 16:30:24,702 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-24 16:30:24,702 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-05-24 16:30:24,863 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-24 16:30:24,868 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
