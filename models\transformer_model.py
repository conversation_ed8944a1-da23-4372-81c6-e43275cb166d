"""
Transformer-based model for stock price prediction.
This model uses a transformer architecture to capture long-range dependencies in time series data.
"""
import numpy as np
import pandas as pd
import os
import logging
import joblib
from sklearn.preprocessing import MinMaxScaler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check if TensorFlow is available
try:
    import tensorflow as tf  # type: ignore
    from tensorflow.keras.models import Model, Sequential, load_model  # type: ignore
    from tensorflow.keras.layers import (  # type: ignore
        Dense, LSTM, Dropout, Input, MultiHeadAttention,
        LayerNormalization, GlobalAveragePooling1D, Conv1D
    )
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint  # type: ignore
    from tensorflow.keras.optimizers import Adam  # type: ignore

    TENSORFLOW_AVAILABLE = True
    logger.info(f"TensorFlow is available for Transformer model. Version: {tf.__version__}")
except ImportError as e:
    TENSORFLOW_AVAILABLE = False
    logger.warning(f"TensorFlow import error: {str(e)}")
    logger.warning("TensorFlow not available. Transformer model will not be functional.")
except Exception as e:
    TENSORFLOW_AVAILABLE = False
    logger.error(f"Error importing TensorFlow: {str(e)}")
    logger.warning("TensorFlow encountered an error. Transformer model will not be functional.")

class TransformerBlock(tf.keras.layers.Layer):
    """
    Transformer block with multi-head attention and feed-forward network.
    """
    def __init__(self, embed_dim=32, num_heads=2, ff_dim=32, rate=0.1, **kwargs):
        super(TransformerBlock, self).__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.rate = rate

        self.att = MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim)
        self.ffn = Sequential([
            Dense(ff_dim, activation="relu"),
            Dense(embed_dim),
        ])
        self.layernorm1 = LayerNormalization(epsilon=1e-6)
        self.layernorm2 = LayerNormalization(epsilon=1e-6)
        self.dropout1 = Dropout(rate)
        self.dropout2 = Dropout(rate)

    def call(self, inputs, training=False):
        attn_output = self.att(inputs, inputs)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)

    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "num_heads": self.num_heads,
            "ff_dim": self.ff_dim,
            "rate": self.rate,
        })
        return config

class StockTransformerModel:
    """
    Transformer-based model for stock price prediction.
    """
    def __init__(self, sequence_length=60, prediction_horizon=1):
        """
        Initialize the model.

        Args:
            sequence_length (int): Number of time steps to look back
            prediction_horizon (int): Number of time steps to predict ahead
        """
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))

        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            logger.error("TensorFlow is not available. Cannot use Transformer model.")
            raise ImportError("TensorFlow is required for the Transformer model.")

    def build_model(self, input_shape, embed_dim=32, num_heads=2, ff_dim=32, num_transformer_blocks=2):
        """
        Build the transformer model.

        Args:
            input_shape (tuple): Shape of input data (sequence_length, features)
            embed_dim (int): Embedding dimension
            num_heads (int): Number of attention heads
            ff_dim (int): Feed-forward network dimension
            num_transformer_blocks (int): Number of transformer blocks

        Returns:
            tf.keras.Model: Compiled model
        """
        inputs = Input(shape=input_shape)

        # Add initial convolutional layer to capture local patterns
        x = Conv1D(filters=embed_dim, kernel_size=1, activation='relu')(inputs)

        # Add transformer blocks
        for _ in range(num_transformer_blocks):
            x = TransformerBlock(embed_dim, num_heads, ff_dim)(x)

        # Global pooling
        x = GlobalAveragePooling1D()(x)

        # Final prediction layer
        outputs = Dense(1)(x)

        model = Model(inputs=inputs, outputs=outputs)

        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )

        return model

    def prepare_data(self, X, y=None):
        """
        Prepare data for training or prediction.

        Args:
            X (np.ndarray): Input data with shape (samples, sequence_length, features)
            y (np.ndarray, optional): Target values

        Returns:
            tuple: (X_scaled, y_scaled) or X_scaled if y is None
        """
        # Get the number of features
        _, _, n_features = X.shape

        # Reshape to 2D for scaling
        X_reshaped = X.reshape(-1, n_features)

        # Fit scaler if y is provided (training mode)
        if y is not None:
            # Fit scaler on X
            self.scaler.fit(X_reshaped)

            # Scale X
            X_scaled = self.scaler.transform(X_reshaped)

            # Reshape back to 3D
            X_scaled = X_scaled.reshape(X.shape)

            # Scale y
            y_scaled = y.reshape(-1, 1)

            return X_scaled, y_scaled
        else:
            # Transform X using fitted scaler
            X_scaled = self.scaler.transform(X_reshaped)

            # Reshape back to 3D
            X_scaled = X_scaled.reshape(X.shape)

            return X_scaled

    def train(self, X_train, y_train, epochs=100, batch_size=32, validation_split=0.2,
              save_path='saved_models', symbol='stock', horizon=None):
        """
        Train the model.

        Args:
            X_train (np.ndarray): Training data with shape (samples, sequence_length, features)
            y_train (np.ndarray): Target values
            epochs (int): Number of training epochs
            batch_size (int): Batch size
            validation_split (float): Fraction of data to use for validation
            save_path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon

        Returns:
            tuple: (model, history)
        """
        # Prepare data
        X_scaled, y_scaled = self.prepare_data(X_train, y_train)

        # Get input shape
        _, seq_len, n_features = X_scaled.shape

        # Build model
        self.model = self.build_model((seq_len, n_features))

        # Define callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            )
        ]

        # Add model checkpoint if save_path is provided
        if save_path and symbol and horizon is not None:
            # Create directory if it doesn't exist
            if not os.path.exists(save_path):
                os.makedirs(save_path)

            # Define checkpoint path
            checkpoint_path = os.path.join(
                save_path,
                f"{symbol}_transformer_{horizon}min.h5"
            )

            # Add checkpoint callback
            callbacks.append(
                ModelCheckpoint(
                    checkpoint_path,
                    monitor='val_loss',
                    save_best_only=True,
                    verbose=1
                )
            )

        # Train model
        logger.info(f"Training Transformer model for {horizon} minutes horizon")
        history = self.model.fit(
            X_scaled, y_scaled,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )

        # Save the model if save_path is provided
        if save_path and symbol and horizon is not None:
            self.save(save_path, symbol, horizon)

        return self.model, history

    def predict(self, X):
        """
        Make predictions.

        Args:
            X (np.ndarray): Input data with shape (samples, sequence_length, features)

        Returns:
            np.ndarray: Predictions
        """
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        # Prepare data
        X_scaled = self.prepare_data(X)

        # Make predictions
        predictions = self.model.predict(X_scaled)

        return predictions.flatten()

    def save(self, path='saved_models', symbol='stock', horizon=None):
        """
        Save the model.

        Args:
            path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon
        """
        if self.model is None:
            raise ValueError("Model not trained")

        # Create directory if it doesn't exist
        if not os.path.exists(path):
            os.makedirs(path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}" if horizon else ""

        # Save model
        model_path = os.path.join(path, f"{symbol}_transformer{horizon_str}.h5")
        try:
            self.model.save(model_path)
            logger.info(f"Saved Transformer model to {model_path}")
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            raise

        # Save scaler
        scaler_path = os.path.join(path, f"{symbol}_transformer_scaler{horizon_str}.joblib")
        try:
            joblib.dump(self.scaler, scaler_path)
            logger.info(f"Saved scaler to {scaler_path}")
        except Exception as e:
            logger.error(f"Error saving scaler: {str(e)}")
            raise

    def load(self, path='saved_models', symbol='stock', horizon=None):
        """
        Load a saved model.

        Args:
            path (str): Path where the model is saved
            symbol (str): Stock symbol
            horizon (int): Prediction horizon

        Returns:
            tf.keras.Model: Loaded model
        """
        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}" if horizon else ""

        # Load model
        model_path = os.path.join(path, f"{symbol}_transformer{horizon_str}.h5")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found at {model_path}")

        # Register custom objects
        custom_objects = {
            'TransformerBlock': TransformerBlock
        }

        # Load model with custom objects
        try:
            self.model = load_model(model_path, custom_objects=custom_objects)
            logger.info(f"Successfully loaded Transformer model from {model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise

        # Load scaler
        scaler_path = os.path.join(path, f"{symbol}_transformer_scaler{horizon_str}.joblib")

        if os.path.exists(scaler_path):
            self.scaler = joblib.load(scaler_path)
            logger.info(f"Loaded scaler from {scaler_path}")
        else:
            logger.warning(f"Scaler not found at {scaler_path}. Using default scaler.")
            self.scaler = MinMaxScaler(feature_range=(0, 1))

        return self.model
