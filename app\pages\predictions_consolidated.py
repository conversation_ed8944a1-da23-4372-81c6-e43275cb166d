"""
Consolidated Predictions Page - Combines all prediction functionality
"""

import streamlit as st
import pandas as pd
import logging
from typing import Optional, Dict, Any

# Import prediction components
from app.components.prediction import prediction_component
from app.components.advanced_prediction import advanced_prediction_component
from app.components.enhanced_prediction import enhanced_prediction_component
from app.components.ensemble_predictions import ensemble_predictions_component

# Import utilities
from app.utils.data_processing import is_model_trained
from app.utils.state_manager import get_available_stock_files

# Configure logging
logger = logging.getLogger(__name__)

def show_predictions_consolidated():
    """Main function to display the consolidated Predictions page"""

    st.title("🔮 AI Predictions")
    st.markdown("### Advanced Stock Price Forecasting with Multiple AI Models")

    # Check if we have data
    if st.session_state.historical_data is None:
        st.warning("⚠️ Please upload historical data first")

        # Quick navigation
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📤 Upload Data", type="primary", use_container_width=True):
                st.session_state.page = "Upload Data"
                st.rerun()
        with col2:
            if st.button("📊 Select Stock", use_container_width=True):
                st.session_state.page = "Stock Management"
                st.rerun()
        return

    # Display current stock info
    if st.session_state.symbol:
        st.info(f"📈 Current Stock: **{st.session_state.symbol}**")

    # Check for trained models
    symbol = st.session_state.symbol
    if symbol:
        # Check if any models are trained
        common_horizons = [4, 15, 30, 60, 1440, 10080]  # minutes, 1 day, 1 week
        trained_models = []

        for horizon in common_horizons:
            if is_model_trained(symbol, horizon, 'rf') or is_model_trained(symbol, horizon, 'lstm'):
                trained_models.append(horizon)

        if not trained_models:
            st.warning("⚠️ No trained models found for this stock.")
            st.info("You need to train models before making predictions.")

            if st.button("🤖 Train Models Now", type="primary"):
                st.session_state.page = "Train Model"
                st.rerun()
            return
        else:
            st.success(f"✅ Found trained models for {len(trained_models)} horizons")

    # Create tabs for different prediction types
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🚀 Quick Predictions",
        "🔬 Advanced Analysis",
        "🎯 Ensemble Models",
        "⚡ Enhanced Predictions",
        "📊 Model Comparison"
    ])

    with tab1:
        show_quick_predictions()

    with tab2:
        show_advanced_predictions()

    with tab3:
        show_ensemble_predictions()

    with tab4:
        show_enhanced_predictions()

    with tab5:
        show_model_comparison()

def show_quick_predictions():
    """Quick and simple predictions interface"""

    st.header("🚀 Quick Predictions")
    st.markdown("Get fast predictions with minimal configuration.")

    # Simple interface
    col1, col2 = st.columns(2)

    with col1:
        # Prediction horizon selection
        horizon_type = st.selectbox(
            "Prediction Timeframe",
            ["Short-term (minutes)", "Medium-term (hours)", "Long-term (days)"],
            key="quick_pred_timeframe"
        )

        if horizon_type == "Short-term (minutes)":
            horizons = [4, 15, 30, 60]
            unit = "minutes"
        elif horizon_type == "Medium-term (hours)":
            horizons = [120, 240, 480, 720]  # 2h, 4h, 8h, 12h
            unit = "minutes"
        else:  # Long-term
            horizons = [1440, 2880, 7200, 10080]  # 1d, 2d, 5d, 1w
            unit = "minutes"

        selected_horizon = st.selectbox(
            f"Select horizon ({unit})",
            horizons,
            key="quick_pred_horizon"
        )

    with col2:
        # Model selection
        model_type = st.selectbox(
            "Model Type",
            ["Auto (Best Available)", "Random Forest", "LSTM", "Ensemble"],
            key="quick_pred_model"
        )

        # Convert display names to internal names
        model_mapping = {
            "Auto (Best Available)": "auto",
            "Random Forest": "rf",
            "LSTM": "lstm",
            "Ensemble": "ensemble"
        }

        internal_model = model_mapping[model_type]

    # Check if we have the required data
    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    if st.session_state.historical_data is None:
        st.warning("Please load historical data first.")
        return

    # Prediction button
    if st.button("🔮 Generate Prediction", type="primary", use_container_width=True):
        with st.spinner("Generating prediction..."):
            try:
                # Import prediction functions
                from models.predict import predict_future_prices
                from scrapers.price_scraper import PriceScraper
                import pandas as pd
                import plotly.graph_objects as go

                # Map model type to internal model name
                model_mapping = {
                    "Auto (Best Available)": "auto",
                    "Random Forest": "rf",
                    "Gradient Boosting": "gb",
                    "LSTM Neural Network": "lstm"
                }

                internal_model = model_mapping[model_type]

                # Check if model is trained
                from app.utils.data_processing import is_model_trained
                if not is_model_trained(st.session_state.symbol, selected_horizon, internal_model, 'saved_models', 'minutes'):
                    st.error(f"No trained model found for {selected_horizon} minute horizon with {model_type}.")
                    st.info("Please train a model first on the Train Model page.")
                    return

                # Fetch fresh live data
                current_live_data = None
                try:
                    with st.spinner("Fetching latest price data..."):
                        scraper = PriceScraper(source="tradingview")
                        price_data = scraper.get_price(st.session_state.symbol)
                        scraper.close_driver()

                        if price_data:
                            current_live_data = pd.DataFrame([price_data])
                            st.success(f"Live price fetched: ${price_data['Close']:.2f}")
                        else:
                            st.warning("Could not fetch live price. Using historical data.")
                except Exception as e:
                    st.warning(f"Error fetching live data: {str(e)}. Using historical data.")

                # Generate prediction
                prediction_data = current_live_data if current_live_data is not None else st.session_state.historical_data

                predictions = predict_future_prices(
                    prediction_data,
                    st.session_state.symbol,
                    horizons=[selected_horizon],
                    model_type=internal_model,
                    models_path='saved_models'
                )

                if selected_horizon in predictions:
                    predicted_price = predictions[selected_horizon]

                    # Calculate prediction time
                    from datetime import datetime, timedelta
                    current_time = datetime.now()
                    pred_time = current_time + timedelta(minutes=selected_horizon)

                    # Get current price
                    if current_live_data is not None:
                        current_price = current_live_data['Close'].iloc[-1]
                    else:
                        current_price = st.session_state.historical_data['Close'].iloc[-1]

                    # Calculate change
                    price_change = predicted_price - current_price
                    percent_change = (price_change / current_price) * 100

                    # Display results
                    st.success("Prediction generated successfully!")

                    # Create columns for results
                    result_col1, result_col2, result_col3 = st.columns(3)

                    with result_col1:
                        st.metric(
                            "Current Price",
                            f"${current_price:.2f}",
                            delta=None
                        )

                    with result_col2:
                        st.metric(
                            f"Predicted Price ({selected_horizon} min)",
                            f"${predicted_price:.2f}",
                            delta=f"${price_change:.2f}"
                        )

                    with result_col3:
                        st.metric(
                            "Expected Change",
                            f"{percent_change:+.2f}%",
                            delta=None
                        )

                    # Show prediction details
                    st.info(f"**Prediction Details:**\n"
                           f"- Stock: {st.session_state.symbol}\n"
                           f"- Model: {model_type}\n"
                           f"- Horizon: {selected_horizon} minutes\n"
                           f"- Prediction Time: {pred_time.strftime('%Y-%m-%d %H:%M:%S')}")

                    # Create a simple chart
                    import plotly.graph_objects as go

                    fig = go.Figure()

                    # Add historical data (last 30 points)
                    hist_data = st.session_state.historical_data.tail(30)
                    fig.add_trace(go.Scatter(
                        x=hist_data['Date'],
                        y=hist_data['Close'],
                        mode='lines',
                        name='Historical',
                        line=dict(color='blue')
                    ))

                    # Add current price point
                    if current_live_data is not None:
                        fig.add_trace(go.Scatter(
                            x=current_live_data['Date'],
                            y=current_live_data['Close'],
                            mode='markers',
                            name='Current',
                            marker=dict(color='green', size=10)
                        ))

                    # Add prediction point
                    fig.add_trace(go.Scatter(
                        x=[pred_time],
                        y=[predicted_price],
                        mode='markers',
                        name='Prediction',
                        marker=dict(color='red', size=12, symbol='star')
                    ))

                    # Add trend line
                    if current_live_data is not None:
                        last_time = current_live_data['Date'].iloc[-1]
                        last_price = current_live_data['Close'].iloc[-1]
                    else:
                        last_time = st.session_state.historical_data['Date'].iloc[-1]
                        last_price = st.session_state.historical_data['Close'].iloc[-1]

                    fig.add_trace(go.Scatter(
                        x=[last_time, pred_time],
                        y=[last_price, predicted_price],
                        mode='lines',
                        name='Trend',
                        line=dict(color='red', dash='dot')
                    ))

                    fig.update_layout(
                        title=f'{st.session_state.symbol} Quick Prediction',
                        xaxis_title='Date',
                        yaxis_title='Price',
                        hovermode='x unified'
                    )

                    st.plotly_chart(fig, use_container_width=True)

                    # Store prediction in session state
                    st.session_state.last_quick_prediction = {
                        'symbol': st.session_state.symbol,
                        'model': model_type,
                        'horizon': selected_horizon,
                        'current_price': current_price,
                        'predicted_price': predicted_price,
                        'prediction_time': pred_time,
                        'generated_at': current_time
                    }

                else:
                    st.error("Failed to generate prediction. Please try again.")

            except Exception as e:
                st.error(f"Error generating prediction: {str(e)}")
                logger.error(f"Quick prediction error: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

def show_advanced_predictions():
    """Advanced predictions with full configuration"""

    st.header("🔬 Advanced Analysis")
    st.markdown("Comprehensive predictions with detailed configuration and analysis.")

    # Use the advanced prediction component
    try:
        advanced_prediction_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in advanced predictions: {str(e)}")
        logger.error(f"Advanced prediction error: {str(e)}")

def show_ensemble_predictions():
    """Ensemble model predictions"""

    st.header("🎯 Ensemble Models")
    st.markdown("Combine multiple models for improved accuracy and reliability.")

    # Add ensemble explanation
    with st.expander("ℹ️ About Ensemble Models"):
        st.markdown("""
        **Ensemble models combine predictions from multiple individual models to create more accurate and robust forecasts.**

        **Benefits:**
        - **Higher Accuracy**: Combines strengths of different models
        - **Reduced Risk**: Less likely to make extreme errors
        - **Better Generalization**: Works well across different market conditions

        **Available Ensemble Types:**
        - **Simple Average**: Equal weight to all models
        - **Weighted Average**: Performance-based weighting
        - **Stacking**: Advanced meta-learning approach
        """)

    # Use the ensemble predictions component
    try:
        ensemble_predictions_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in ensemble predictions: {str(e)}")
        logger.error(f"Ensemble prediction error: {str(e)}")

def show_enhanced_predictions():
    """Enhanced predictions with adaptive features"""

    st.header("⚡ Enhanced Predictions")
    st.markdown("Next-generation predictions with adaptive model selection and confidence intervals.")

    # Add enhanced features explanation
    with st.expander("✨ Enhanced Features"):
        st.markdown("""
        **Enhanced predictions include advanced features:**

        - **Adaptive Model Selection**: Automatically chooses the best model for current conditions
        - **Confidence Intervals**: Provides uncertainty estimates with predictions
        - **Market Regime Detection**: Adjusts predictions based on market conditions
        - **Real-time Optimization**: Continuously improves based on recent performance
        """)

    # Use the enhanced prediction component
    try:
        enhanced_prediction_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in enhanced predictions: {str(e)}")
        logger.error(f"Enhanced prediction error: {str(e)}")

def show_model_comparison():
    """Compare different models and their performance"""

    st.header("📊 Model Comparison")
    st.markdown("Compare performance across different models and horizons.")

    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    symbol = st.session_state.symbol

    # Model performance comparison
    st.subheader("🏆 Model Performance Overview")

    # Check available models
    model_types = ['rf', 'lstm', 'gb', 'lr', 'ensemble']
    horizons = [4, 15, 30, 60, 1440]

    # Create performance matrix
    performance_data = []

    for model in model_types:
        model_row = {'Model': model.upper()}
        for horizon in horizons:
            if is_model_trained(symbol, horizon, model):
                model_row[f'{horizon}min'] = '✅ Trained'
            else:
                model_row[f'{horizon}min'] = '❌ Not Trained'
        performance_data.append(model_row)

    if performance_data:
        df_performance = pd.DataFrame(performance_data)
        st.dataframe(df_performance, use_container_width=True)

    # Model recommendations
    st.subheader("💡 Model Recommendations")

    recommendations = {
        "Short-term (< 1 hour)": {
            "Best Models": ["LSTM", "Random Forest"],
            "Reason": "Neural networks excel at capturing short-term patterns"
        },
        "Medium-term (1-24 hours)": {
            "Best Models": ["Ensemble", "Gradient Boosting"],
            "Reason": "Ensemble methods provide stability for medium-term forecasts"
        },
        "Long-term (> 1 day)": {
            "Best Models": ["Random Forest", "Linear Regression"],
            "Reason": "Traditional ML models handle long-term trends well"
        }
    }

    for timeframe, info in recommendations.items():
        with st.expander(f"📈 {timeframe}"):
            st.write(f"**Recommended Models:** {', '.join(info['Best Models'])}")
            st.write(f"**Reason:** {info['Reason']}")

    # Quick training suggestions
    st.subheader("🚀 Quick Actions")

    action_cols = st.columns(3)

    with action_cols[0]:
        if st.button("🤖 Train More Models", use_container_width=True):
            st.session_state.page = "Train Model"
            st.rerun()

    with action_cols[1]:
        if st.button("📊 View Performance Metrics", use_container_width=True):
            st.session_state.page = "Performance Metrics"
            st.rerun()

    with action_cols[2]:
        if st.button("📈 Live Trading", use_container_width=True):
            st.session_state.page = "Live Trading"
            st.rerun()
