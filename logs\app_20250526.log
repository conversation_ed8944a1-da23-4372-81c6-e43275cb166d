2025-05-26 11:52:59,874 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-26 11:53:05,395 - app - INFO - Memory management utilities loaded
2025-05-26 11:53:05,406 - app - INFO - Error handling utilities loaded
2025-05-26 11:53:05,412 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-26 11:53:05,414 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-26 11:53:05,414 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-26 11:53:05,415 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-26 11:53:05,426 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-26 11:53:05,440 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-26 11:53:05,441 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-26 11:53:05,441 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-26 11:53:05,441 - app - INFO - Applied NumPy fix
2025-05-26 11:53:05,453 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-26 11:53:05,454 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-26 11:53:05,455 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-26 11:53:05,455 - app - INFO - Applied NumPy BitGenerator fix
2025-05-26 11:53:23,666 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-26 11:53:23,667 - app - INFO - Applied TensorFlow fix
2025-05-26 11:53:23,687 - app.config - INFO - Configuration initialized
2025-05-26 11:53:23,705 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-26 11:53:24,011 - models.train - INFO - TensorFlow test successful
2025-05-26 11:53:28,521 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-26 11:53:28,521 - models.train - INFO - Transformer model is available
2025-05-26 11:53:28,521 - models.train - INFO - Using TensorFlow-based models
2025-05-26 11:53:28,530 - models.predict - INFO - Transformer model is available for predictions
2025-05-26 11:53:28,531 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-26 11:53:28,559 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-26 11:53:30,584 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-26 11:53:30,585 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-26 11:53:30,585 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-26 11:53:30,586 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-26 11:53:30,587 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-26 11:53:31,624 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-26 11:53:31,640 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:32,485 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-26 11:53:35,792 - app.services.llm_service - INFO - llama_cpp is available
2025-05-26 11:53:35,837 - app.utils.session_state - INFO - Initializing session state
2025-05-26 11:53:35,838 - app.utils.session_state - INFO - Session state initialized
2025-05-26 11:53:36,986 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 11:53:36,996 - app.utils.memory_management - INFO - Memory before cleanup: 427.66 MB
2025-05-26 11:53:37,117 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-26 11:53:37,117 - app.utils.memory_management - INFO - Memory after cleanup: 427.66 MB (freed -0.00 MB)
2025-05-26 11:53:54,220 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:54,252 - app.utils.memory_management - INFO - Memory before cleanup: 429.23 MB
2025-05-26 11:53:54,412 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-26 11:53:54,413 - app.utils.memory_management - INFO - Memory after cleanup: 429.25 MB (freed -0.03 MB)
2025-05-26 11:53:55,469 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:55,599 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.09 seconds
2025-05-26 11:53:55,619 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 11:53:55,619 - app - INFO - Data shape: (576, 6)
2025-05-26 11:53:55,619 - app - INFO - File COMI contains 2025 data
2025-05-26 11:53:55,651 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-26 11:53:55,651 - app - INFO - Features shape: (576, 36)
2025-05-26 11:53:55,671 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-26 11:53:55,671 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 11:53:55,672 - app - INFO - Data shape: (576, 6)
2025-05-26 11:53:55,672 - app - INFO - File COMI contains 2025 data
2025-05-26 11:53:55,674 - app.utils.memory_management - INFO - Memory before cleanup: 432.84 MB
2025-05-26 11:53:55,802 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-05-26 11:53:55,802 - app.utils.memory_management - INFO - Memory after cleanup: 432.88 MB (freed -0.04 MB)
2025-05-26 11:53:55,936 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:53:56,020 - app.utils.memory_management - INFO - Memory before cleanup: 433.80 MB
2025-05-26 11:53:56,144 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-26 11:53:56,145 - app.utils.memory_management - INFO - Memory after cleanup: 433.80 MB (freed 0.00 MB)
2025-05-26 11:54:30,116 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:54:30,150 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:54:31,498 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:54:31,719 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:54:31,719 - app.utils.error_handling - INFO - live_trading_component executed in 1.58 seconds
2025-05-26 11:54:31,719 - app.utils.memory_management - INFO - Memory before cleanup: 436.54 MB
2025-05-26 11:54:31,865 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-26 11:54:31,866 - app.utils.memory_management - INFO - Memory after cleanup: 436.54 MB (freed 0.00 MB)
2025-05-26 11:55:10,663 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:55:10,696 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:55:13,023 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:55:13,023 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:55:14,160 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:55:14,170 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:55:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:55:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:55:14,172 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:55:14,175 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:55:14,180 - app.utils.error_handling - INFO - live_trading_component executed in 3.50 seconds
2025-05-26 11:55:14,181 - app.utils.memory_management - INFO - Memory before cleanup: 437.75 MB
2025-05-26 11:55:14,304 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-05-26 11:55:14,305 - app.utils.memory_management - INFO - Memory after cleanup: 437.75 MB (freed 0.00 MB)
2025-05-26 11:56:17,243 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:56:17,318 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:56:17,319 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:56:17,321 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:56:17,322 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:56:17,327 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:56:17,333 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-26 11:56:17,334 - app.utils.memory_management - INFO - Memory before cleanup: 432.80 MB
2025-05-26 11:56:17,503 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-26 11:56:17,503 - app.utils.memory_management - INFO - Memory after cleanup: 432.89 MB (freed -0.10 MB)
2025-05-26 11:57:36,843 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:57:36,869 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:57:38,984 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:57:38,984 - scrapers.price_scraper - INFO - Account type: Standard TradingView
2025-05-26 11:57:38,984 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:57:40,165 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:57:40,174 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:57:40,175 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:57:40,176 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:57:40,177 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:57:40,182 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:57:40,183 - app.utils.error_handling - INFO - live_trading_component executed in 3.33 seconds
2025-05-26 11:57:40,185 - app.utils.memory_management - INFO - Memory before cleanup: 432.97 MB
2025-05-26 11:57:40,310 - app.utils.memory_management - INFO - Garbage collection: collected 252 objects
2025-05-26 11:57:40,311 - app.utils.memory_management - INFO - Memory after cleanup: 432.97 MB (freed 0.00 MB)
2025-05-26 11:58:11,009 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:58:11,045 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:58:13,102 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:58:13,102 - scrapers.price_scraper - INFO - Account type: Standard TradingView
2025-05-26 11:58:13,102 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:58:14,209 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:58:14,209 - scrapers.price_scraper - INFO - Using standard authentication flow for TradingView
2025-05-26 11:58:14,423 - scrapers.price_scraper - WARNING - Error checking logout UI elements: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=136.0.7103.114); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3E10ABC]
	(No symbol) [0x00007FF7B3E13B1C]
	(No symbol) [0x00007FF7B3E13BEF]
	(No symbol) [0x00007FF7B3E5E9F7]
	(No symbol) [0x00007FF7B3E5F3FC]
	(No symbol) [0x00007FF7B3EB2497]
	(No symbol) [0x00007FF7B3E8712F]
	(No symbol) [0x00007FF7B3EAF2BB]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:14,423 - scrapers.price_scraper - WARNING - Error checking login via JavaScript: Message: invalid element state: Failed to execute 'querySelectorAll' on 'Document': 'a[href*="signin"], button:contains("Sign in"), .tv-header__link--signin' is not a valid selector.
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3E10ABC]
	(No symbol) [0x00007FF7B3E13EAC]
	(No symbol) [0x00007FF7B3EB04AB]
	(No symbol) [0x00007FF7B3E870EA]
	(No symbol) [0x00007FF7B3EAF2BB]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:14,423 - scrapers.price_scraper - INFO - Checking login by navigating to chart page...
2025-05-26 11:58:20,192 - scrapers.price_scraper - WARNING - Error checking login via protected page: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:20,208 - scrapers.price_scraper - WARNING - Error checking real-time data access: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:20,210 - scrapers.price_scraper - INFO - Could not confirm login status after all checks, assuming not logged in
2025-05-26 11:58:20,210 - scrapers.price_scraper - INFO - Navigating directly to TradingView sign-in page
2025-05-26 11:58:20,213 - scrapers.price_scraper - ERROR - Error during TradingView login: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:20,229 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:58:20,230 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:58:20,232 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:58:20,233 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:58:20,237 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:58:20,240 - app.utils.error_handling - INFO - live_trading_component executed in 9.21 seconds
2025-05-26 11:58:20,242 - app.utils.memory_management - INFO - Memory before cleanup: 433.46 MB
2025-05-26 11:58:20,343 - app.utils.memory_management - INFO - Garbage collection: collected 253 objects
2025-05-26 11:58:20,343 - app.utils.memory_management - INFO - Memory after cleanup: 433.46 MB (freed 0.00 MB)
2025-05-26 11:58:21,392 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:58:21,416 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 11:58:21,420 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.7
2025-05-26 11:58:21,541 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 11:58:21,542 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 11:58:21,542 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:58:21,543 - app.utils.retry - WARNING - Attempt 1 failed, retrying in 2.04s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:23,593 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:58:23,594 - app.utils.retry - WARNING - Attempt 2 failed, retrying in 3.59s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:27,198 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:58:27,199 - app.utils.retry - WARNING - Attempt 3 failed, retrying in 8.30s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:35,505 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:58:35,507 - app.utils.retry - ERROR - Failed after 3 attempts: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:35,507 - scrapers.price_scraper - ERROR - Error navigating to https://www.tradingview.com/symbols/EGX-COMI/ after retries: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:58:35,507 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 11:58:35,524 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.51
2025-05-26 11:58:35,524 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 11:58:35,524 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 11:58:35,524 - app.utils.error_handling - INFO - fetch_price executed in 13.98 seconds
2025-05-26 11:58:35,596 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:58:35,597 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:58:35,597 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:58:35,598 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:58:35,602 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:58:35,605 - app.utils.error_handling - INFO - live_trading_component executed in 14.20 seconds
2025-05-26 11:58:35,605 - app.utils.memory_management - INFO - Memory before cleanup: 434.80 MB
2025-05-26 11:58:35,720 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-26 11:58:35,721 - app.utils.memory_management - INFO - Memory after cleanup: 434.80 MB (freed 0.00 MB)
2025-05-26 11:59:09,913 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:59:09,951 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 11:59:09,973 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 11:59:09,976 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.59
2025-05-26 11:59:09,977 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 11:59:09,977 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 11:59:09,977 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:59:09,979 - app.utils.retry - WARNING - Attempt 1 failed, retrying in 1.65s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:59:11,641 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:59:11,642 - app.utils.retry - WARNING - Attempt 2 failed, retrying in 3.67s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:59:15,327 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:59:15,329 - app.utils.retry - WARNING - Attempt 3 failed, retrying in 7.79s: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:59:23,134 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 11:59:23,136 - app.utils.retry - ERROR - Failed after 3 attempts: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:59:23,136 - scrapers.price_scraper - ERROR - Error navigating to https://www.tradingview.com/symbols/EGX-COMI/ after retries: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3DE1B63]
	(No symbol) [0x00007FF7B3E8EB6E]
	(No symbol) [0x00007FF7B3EAEBB2]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 11:59:23,136 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 11:59:23,136 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.96
2025-05-26 11:59:23,136 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 11:59:23,136 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 11:59:23,136 - app.utils.error_handling - INFO - fetch_price executed in 13.16 seconds
2025-05-26 11:59:23,151 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:59:23,151 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:59:23,151 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:59:23,151 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:59:23,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:59:23,167 - app.utils.error_handling - INFO - live_trading_component executed in 13.21 seconds
2025-05-26 11:59:23,168 - app.utils.memory_management - INFO - Memory before cleanup: 435.12 MB
2025-05-26 11:59:23,285 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-05-26 11:59:23,285 - app.utils.memory_management - INFO - Memory after cleanup: 435.12 MB (freed 0.00 MB)
2025-05-26 11:59:29,619 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:59:29,632 - app.utils.memory_management - INFO - Memory before cleanup: 435.07 MB
2025-05-26 11:59:29,741 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-05-26 11:59:29,741 - app.utils.memory_management - INFO - Memory after cleanup: 435.07 MB (freed 0.00 MB)
2025-05-26 11:59:32,841 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:59:32,854 - app.utils.memory_management - INFO - Memory before cleanup: 435.07 MB
2025-05-26 11:59:32,961 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-26 11:59:32,961 - app.utils.memory_management - INFO - Memory after cleanup: 435.07 MB (freed 0.00 MB)
2025-05-26 11:59:34,330 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:59:34,343 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:59:36,397 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:59:36,397 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:59:37,494 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:59:37,527 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:59:37,528 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:59:37,529 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:59:37,529 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:59:37,537 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:59:37,539 - app.utils.error_handling - INFO - live_trading_component executed in 3.20 seconds
2025-05-26 11:59:37,539 - app.utils.memory_management - INFO - Memory before cleanup: 435.09 MB
2025-05-26 11:59:37,649 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-26 11:59:37,650 - app.utils.memory_management - INFO - Memory after cleanup: 435.09 MB (freed 0.00 MB)
2025-05-26 11:59:44,473 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 11:59:44,498 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 11:59:46,589 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 11:59:46,590 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 11:59:47,672 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 11:59:47,683 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 11:59:47,684 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 11:59:47,684 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 11:59:47,685 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 11:59:47,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 11:59:47,690 - app.utils.error_handling - INFO - live_trading_component executed in 3.20 seconds
2025-05-26 11:59:47,692 - app.utils.memory_management - INFO - Memory before cleanup: 435.10 MB
2025-05-26 11:59:47,802 - app.utils.memory_management - INFO - Garbage collection: collected 248 objects
2025-05-26 11:59:47,802 - app.utils.memory_management - INFO - Memory after cleanup: 435.10 MB (freed 0.00 MB)
2025-05-26 12:00:09,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:00:09,358 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:00:11,481 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 12:00:11,482 - scrapers.price_scraper - INFO - Account type: Standard TradingView
2025-05-26 12:00:11,484 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:00:12,651 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:00:12,663 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:00:12,664 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:00:12,665 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:00:12,666 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:00:12,669 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:00:12,670 - app.utils.error_handling - INFO - live_trading_component executed in 3.32 seconds
2025-05-26 12:00:12,671 - app.utils.memory_management - INFO - Memory before cleanup: 435.11 MB
2025-05-26 12:00:12,778 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-05-26 12:00:12,778 - app.utils.memory_management - INFO - Memory after cleanup: 435.11 MB (freed 0.00 MB)
2025-05-26 12:00:23,086 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:00:23,113 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:00:25,201 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-26 12:00:25,201 - scrapers.price_scraper - INFO - Account type: Standard TradingView
2025-05-26 12:00:25,201 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:00:26,309 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:00:26,310 - scrapers.price_scraper - INFO - Using standard authentication flow for TradingView
2025-05-26 12:00:26,536 - scrapers.price_scraper - WARNING - Error checking logout UI elements: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=136.0.7103.114); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3E10ABC]
	(No symbol) [0x00007FF7B3E13B1C]
	(No symbol) [0x00007FF7B3E13BEF]
	(No symbol) [0x00007FF7B3E5E9F7]
	(No symbol) [0x00007FF7B3E5F3FC]
	(No symbol) [0x00007FF7B3EB2497]
	(No symbol) [0x00007FF7B3E8712F]
	(No symbol) [0x00007FF7B3EAF2BB]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 12:00:26,551 - scrapers.price_scraper - WARNING - Error checking login via JavaScript: Message: invalid element state: Failed to execute 'querySelectorAll' on 'Document': 'a[href*="signin"], button:contains("Sign in"), .tv-header__link--signin' is not a valid selector.
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3E10ABC]
	(No symbol) [0x00007FF7B3E13EAC]
	(No symbol) [0x00007FF7B3EB04AB]
	(No symbol) [0x00007FF7B3E870EA]
	(No symbol) [0x00007FF7B3EAF2BB]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 12:00:26,551 - scrapers.price_scraper - INFO - Checking login by navigating to chart page...
2025-05-26 12:00:35,405 - scrapers.price_scraper - INFO - Found chart element using selector: .chart-container
2025-05-26 12:00:35,433 - scrapers.price_scraper - INFO - Already logged in to TradingView
2025-05-26 12:00:35,451 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:00:35,452 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:00:35,452 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:00:35,453 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:00:35,463 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:00:35,465 - app.utils.error_handling - INFO - live_trading_component executed in 12.36 seconds
2025-05-26 12:00:35,466 - app.utils.memory_management - INFO - Memory before cleanup: 435.11 MB
2025-05-26 12:00:35,605 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-05-26 12:00:35,605 - app.utils.memory_management - INFO - Memory after cleanup: 435.11 MB (freed 0.00 MB)
2025-05-26 12:00:37,732 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:00:37,756 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:00:37,759 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.23
2025-05-26 12:00:37,760 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:00:37,760 - scrapers.price_scraper - INFO - Attempting to get real-time price for COMI (logged in: True)
2025-05-26 12:00:43,460 - scrapers.price_scraper - INFO - Setting up chart for EGX:COMI
2025-05-26 12:00:43,518 - scrapers.price_scraper - ERROR - Error setting up chart for COMI: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[class*='symbol'][class*='button'], [class*='Symbol'][class*='button']"}
  (Session info: chrome=136.0.7103.114); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7B4055335+78597]
	GetHandleVerifier [0x00007FF7B4055390+78688]
	(No symbol) [0x00007FF7B3E091AA]
	(No symbol) [0x00007FF7B3E5F149]
	(No symbol) [0x00007FF7B3E5F3FC]
	(No symbol) [0x00007FF7B3EB2467]
	(No symbol) [0x00007FF7B3E8712F]
	(No symbol) [0x00007FF7B3EAF2BB]
	(No symbol) [0x00007FF7B3E86EC3]
	(No symbol) [0x00007FF7B3E503F8]
	(No symbol) [0x00007FF7B3E51163]
	GetHandleVerifier [0x00007FF7B42FEEED+2870973]
	GetHandleVerifier [0x00007FF7B42F9698+2848360]
	GetHandleVerifier [0x00007FF7B4316973+2967875]
	GetHandleVerifier [0x00007FF7B407017A+188746]
	GetHandleVerifier [0x00007FF7B407845F+222255]
	GetHandleVerifier [0x00007FF7B405D2B4+111236]
	GetHandleVerifier [0x00007FF7B405D462+111666]
	GetHandleVerifier [0x00007FF7B4043589+5465]
	BaseThreadInitThunk [0x00007FFA6A1F259D+29]
	RtlUserThreadStart [0x00007FFA6C44AF58+40]

2025-05-26 12:00:43,519 - scrapers.price_scraper - WARNING - Could not get real-time price for COMI
2025-05-26 12:00:43,519 - scrapers.price_scraper - WARNING - Failed to get real-time price for COMI. Falling back to delayed data.
2025-05-26 12:00:43,519 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:00:43,519 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:00:46,648 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:00:48,777 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:00:48,786 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.24
2025-05-26 12:00:48,786 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.24
2025-05-26 12:00:48,786 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:00:48,787 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:00:48,787 - app.utils.error_handling - INFO - fetch_price executed in 11.03 seconds
2025-05-26 12:00:48,808 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:00:48,809 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:00:48,809 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:00:48,810 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:00:48,813 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:00:48,815 - app.utils.error_handling - INFO - live_trading_component executed in 11.07 seconds
2025-05-26 12:00:48,816 - app.utils.memory_management - INFO - Memory before cleanup: 435.18 MB
2025-05-26 12:00:48,931 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-05-26 12:00:48,932 - app.utils.memory_management - INFO - Memory after cleanup: 435.18 MB (freed 0.00 MB)
2025-05-26 12:02:18,013 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:18,059 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:18,060 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:02:18,061 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:18,061 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:02:18,070 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:02:18,072 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-26 12:02:18,075 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-26 12:02:18,076 - app.utils.memory_management - INFO - Memory before cleanup: 435.13 MB
2025-05-26 12:02:18,215 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-26 12:02:18,222 - app.utils.memory_management - INFO - Memory after cleanup: 435.13 MB (freed 0.00 MB)
2025-05-26 12:02:19,942 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:19,967 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:19,967 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:02:19,969 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:19,969 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:02:19,972 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-26 12:02:19,980 - app.utils.error_handling - INFO - live_trading_component executed in 0.03 seconds
2025-05-26 12:02:19,981 - app.utils.memory_management - INFO - Memory before cleanup: 435.14 MB
2025-05-26 12:02:20,090 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-26 12:02:20,090 - app.utils.memory_management - INFO - Memory after cleanup: 435.14 MB (freed 0.00 MB)
2025-05-26 12:02:24,891 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:24,918 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:24,919 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:02:24,919 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:24,920 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:02:24,923 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-26 12:02:24,923 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:02:24,926 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-26 12:02:24,926 - app.utils.memory_management - INFO - Memory before cleanup: 435.14 MB
2025-05-26 12:02:25,039 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-26 12:02:25,040 - app.utils.memory_management - INFO - Memory after cleanup: 435.14 MB (freed 0.00 MB)
2025-05-26 12:02:26,988 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:27,014 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:27,014 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:02:27,015 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:27,015 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:02:27,018 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-26 12:02:27,019 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:02:27,020 - app.utils.data_processing - INFO - Found lstm model for COMI with 25 minutes horizon
2025-05-26 12:02:27,022 - app.utils.error_handling - INFO - live_trading_component executed in 0.02 seconds
2025-05-26 12:02:27,023 - app.utils.memory_management - INFO - Memory before cleanup: 435.14 MB
2025-05-26 12:02:27,135 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-26 12:02:27,136 - app.utils.memory_management - INFO - Memory after cleanup: 435.14 MB (freed 0.00 MB)
2025-05-26 12:02:31,024 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:31,051 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:31,052 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-26 12:02:31,052 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:31,053 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-26 12:02:31,056 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-26 12:02:31,057 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:02:31,058 - app.utils.data_processing - INFO - Found lstm model for COMI with 25 minutes horizon
2025-05-26 12:02:31,094 - models.predict - INFO - Making predictions for 69 minutes horizon
2025-05-26 12:02:31,205 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_69_scaler.pkl (0.53 KB)
2025-05-26 12:02:31,313 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:02:31,322 - models.predict - INFO - Loading lstm model for COMI with horizon 69
2025-05-26 12:02:31,323 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_69min.keras
2025-05-26 12:02:32,700 - models.predict - INFO - Successfully loaded model for COMI with horizon 69
2025-05-26 12:02:34,565 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:02:34,565 - models.predict - INFO - Current price: 80.24, Predicted scaled value: 0.5871987342834473
2025-05-26 12:02:34,566 - models.predict - INFO - Prediction for 69 minutes horizon: 73.878489299948
2025-05-26 12:02:34,566 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:02:34,681 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:02:34,791 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:02:34,793 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-26 12:02:34,793 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-26 12:02:35,409 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:02:36,290 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:02:36,290 - models.predict - INFO - Current price: 80.24, Predicted scaled value: 0.63470858335495
2025-05-26 12:02:36,290 - models.predict - INFO - Prediction for 30 minutes horizon: 76.48202888358223
2025-05-26 12:02:36,291 - models.predict - INFO - Making predictions for 25 minutes horizon
2025-05-26 12:02:36,420 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_25_scaler.pkl (0.53 KB)
2025-05-26 12:02:36,530 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:02:36,532 - models.predict - INFO - Loading lstm model for COMI with horizon 25
2025-05-26 12:02:36,533 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_25min.keras
2025-05-26 12:02:37,178 - models.predict - INFO - Successfully loaded model for COMI with horizon 25
2025-05-26 12:02:38,184 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:02:38,184 - models.predict - INFO - Current price: 80.24, Predicted scaled value: 0.5709803104400635
2025-05-26 12:02:38,185 - models.predict - INFO - Prediction for 25 minutes horizon: 72.98971972299444
2025-05-26 12:02:38,266 - app.utils.error_handling - INFO - live_trading_component executed in 7.23 seconds
2025-05-26 12:02:38,266 - app.utils.memory_management - INFO - Memory before cleanup: 506.31 MB
2025-05-26 12:02:38,466 - app.utils.memory_management - INFO - Garbage collection: collected 24277 objects
2025-05-26 12:02:38,466 - app.utils.memory_management - INFO - Memory after cleanup: 472.86 MB (freed 33.46 MB)
2025-05-26 12:02:57,466 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:02:57,479 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:57,480 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:02:57,480 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:57,485 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,486 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:02:57,486 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:02:57,499 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:02:57,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:02:57,507 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:02:57,512 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,516 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:02:57,517 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:02:57,517 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:02:57,517 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:02:57,518 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:02:57,518 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:02:57,519 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:02:57,519 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:02:57,520 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:02:57,520 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:02:57,520 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:02:57,520 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:02:57,521 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:02:57,521 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:02:57,521 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:02:57,522 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:02:57,523 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:02:57,523 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:02:57,523 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:02:57,635 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:02:57,656 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:57,656 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:02:57,656 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:57,664 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,665 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:02:57,666 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:02:57,673 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:02:57,673 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:02:57,678 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,683 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:02:57,684 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:02:57,684 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:02:57,684 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:02:57,688 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,689 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:02:57,689 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:02:57,689 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:02:57,689 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:02:57,694 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,694 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:02:57,695 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:02:57,695 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:02:57,696 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:02:57,700 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:02:57,700 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:02:57,707 - app.utils.memory_management - INFO - Memory before cleanup: 474.24 MB
2025-05-26 12:02:57,816 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:02:57,817 - app.utils.memory_management - INFO - Memory after cleanup: 474.24 MB (freed 0.00 MB)
2025-05-26 12:03:34,976 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:03:35,001 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:03:35,009 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:03:35,009 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:03:35,018 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:35,031 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:03:35,032 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:03:35,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:03:35,067 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:03:35,068 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:03:35,074 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:35,082 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:03:36,298 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:03:36,298 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:03:36,303 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.0
2025-05-26 12:03:36,303 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:03:36,303 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:03:36,304 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:03:42,479 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:03:44,607 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:03:44,620 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.38
2025-05-26 12:03:44,620 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.38
2025-05-26 12:03:44,621 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:03:44,621 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:03:44,621 - app.utils.error_handling - INFO - fetch_price executed in 8.32 seconds
2025-05-26 12:03:44,625 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:03:46,744 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-26 12:03:46,840 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-26 12:03:46,956 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:03:46,956 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-05-26 12:03:46,956 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_5min.keras
2025-05-26 12:03:47,657 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-26 12:03:48,557 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:03:48,557 - models.predict - INFO - Current price: 80.38, Predicted scaled value: 0.7200331091880798
2025-05-26 12:03:48,557 - models.predict - INFO - Prediction for 5 minutes horizon: 81.15781438350677
2025-05-26 12:03:48,557 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:03:48,675 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:03:48,791 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:03:48,791 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-26 12:03:48,791 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:03:48,791 - models.predict - WARNING - Model file not found or import error for lstm with horizon 15: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:03:48,791 - models.predict - INFO - Skipping lstm model for horizon 15 - model not trained for this horizon
2025-05-26 12:03:48,791 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:03:48,924 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:03:49,041 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:03:49,041 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-26 12:03:49,041 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-26 12:03:49,642 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:03:50,676 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001F93786D3F0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-26 12:03:50,676 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:03:50,676 - models.predict - INFO - Current price: 80.38, Predicted scaled value: 0.7038465738296509
2025-05-26 12:03:50,676 - models.predict - INFO - Prediction for 30 minutes horizon: 80.27079054988228
2025-05-26 12:03:50,676 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:03:50,810 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:03:50,943 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:03:50,943 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-26 12:03:50,943 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:03:50,943 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:03:50,943 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-26 12:03:50,943 - app.components.advanced_prediction - WARNING - No prediction found for horizon 15 (minutes)
2025-05-26 12:03:50,943 - app.components.advanced_prediction - INFO - Using prediction from horizon 5 minutes for 15 minutes
2025-05-26 12:03:50,943 - app.components.advanced_prediction - WARNING - No prediction found for horizon 60 (minutes)
2025-05-26 12:03:50,943 - app.components.advanced_prediction - INFO - Using prediction from horizon 30 minutes for 60 minutes
2025-05-26 12:03:50,996 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 5 minutes
2025-05-26 12:03:51,011 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-26 12:03:51,025 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-26 12:03:51,042 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:03:51,060 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:03:51,060 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:03:51,210 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:03:51,234 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:03:51,235 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:03:51,235 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:03:51,244 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:51,245 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:03:51,245 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:03:51,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:03:51,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:03:51,254 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:51,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:03:51,258 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:03:51,258 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:03:51,259 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:03:51,266 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:51,266 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:03:51,267 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:03:51,267 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:03:51,267 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:03:51,271 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:51,272 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:03:51,272 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:03:51,273 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:03:51,273 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:03:51,278 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:03:51,278 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:03:51,278 - app.utils.memory_management - INFO - Memory before cleanup: 480.27 MB
2025-05-26 12:03:51,440 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-26 12:03:51,449 - app.utils.memory_management - INFO - Memory after cleanup: 480.33 MB (freed -0.06 MB)
2025-05-26 12:04:52,165 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:04:52,190 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 12:04:52,200 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:04:52,206 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:04:52,207 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:04:52,211 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,213 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:04:52,213 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:04:52,233 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:04:52,239 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:04:52,239 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:04:52,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,240 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:04:52,240 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:04:52,240 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:04:52,240 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:04:52,253 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:04:52,255 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:04:52,256 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:04:52,256 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:04:52,256 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:04:52,257 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:04:52,411 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:04:52,440 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:04:52,441 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:04:52,441 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:04:52,447 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,447 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:04:52,448 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:04:52,452 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:04:52,453 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:04:52,458 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,458 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:04:52,458 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:04:52,458 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:04:52,458 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:04:52,472 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,472 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:04:52,473 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:04:52,473 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:04:52,473 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:04:52,477 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,477 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:04:52,478 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:04:52,478 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:04:52,478 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:04:52,484 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:04:52,484 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:04:52,492 - app.utils.memory_management - INFO - Memory before cleanup: 471.57 MB
2025-05-26 12:04:52,607 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:04:52,607 - app.utils.memory_management - INFO - Memory after cleanup: 471.57 MB (freed 0.00 MB)
2025-05-26 12:05:05,737 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:05,747 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:05,747 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:05,749 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:05,754 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,754 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:05,754 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:05,766 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:05,775 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:05,775 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:05,779 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,784 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:05,784 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:05,785 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:05,785 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:05,785 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:05,786 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:05,787 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:05,787 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:05,787 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:05,788 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:05,900 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:05,920 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:05,921 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:05,921 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:05,929 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,929 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:05,930 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:05,936 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:05,937 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:05,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:05,939 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:05,939 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:05,939 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:05,951 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,951 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:05,952 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:05,952 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:05,952 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:05,957 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,957 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:05,957 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:05,957 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:05,958 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:05,962 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:05,962 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:05,970 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-05-26 12:05:06,165 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:06,166 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-05-26 12:05:18,716 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:18,731 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:18,731 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:18,732 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:18,738 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,739 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:18,739 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:18,751 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:18,760 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:18,761 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:18,765 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:18,768 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:18,768 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:18,894 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:18,917 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:18,933 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:18,933 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:18,937 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,941 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:18,943 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:18,943 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:18,944 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:18,948 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,949 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:18,949 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:18,949 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:18,950 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:18,954 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,954 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:18,954 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:18,955 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:18,955 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:18,959 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:18,959 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:18,968 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-05-26 12:05:19,087 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:19,106 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-05-26 12:05:26,202 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:26,210 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:26,210 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:26,210 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:26,221 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,221 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:26,222 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:26,234 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:26,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:26,242 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:26,246 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,249 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:26,249 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:26,250 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:26,251 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:26,251 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:26,254 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:26,255 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:26,256 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:26,256 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:26,256 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:26,369 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:26,385 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:26,385 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:26,385 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:26,404 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,405 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:26,405 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:26,405 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:26,405 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:26,405 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:26,419 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:26,438 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:26,439 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:26,444 - app.utils.memory_management - INFO - Memory before cleanup: 471.56 MB
2025-05-26 12:05:26,559 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:26,560 - app.utils.memory_management - INFO - Memory after cleanup: 471.56 MB (freed 0.00 MB)
2025-05-26 12:05:27,157 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:27,169 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:27,170 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:27,171 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:27,177 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,177 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:27,178 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:27,189 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:27,199 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:27,200 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:27,207 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:27,207 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:27,207 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:27,320 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:27,343 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:27,343 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:27,343 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:27,355 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,355 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:27,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:27,361 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:27,362 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:27,366 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:27,369 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:27,390 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:27,390 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:27,399 - app.utils.memory_management - INFO - Memory before cleanup: 471.57 MB
2025-05-26 12:05:27,503 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:27,503 - app.utils.memory_management - INFO - Memory after cleanup: 471.57 MB (freed 0.00 MB)
2025-05-26 12:05:27,997 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:28,007 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:28,031 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:28,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:28,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:28,044 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,049 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:28,049 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:28,049 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:28,049 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:28,049 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:28,054 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:28,055 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:28,056 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:28,056 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:28,056 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:28,166 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:28,185 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:28,185 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:28,187 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:28,187 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,187 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:28,187 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:28,203 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:28,204 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:28,205 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:28,220 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:28,240 - app.utils.memory_management - INFO - Memory before cleanup: 471.57 MB
2025-05-26 12:05:28,348 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:28,348 - app.utils.memory_management - INFO - Memory after cleanup: 471.57 MB (freed 0.00 MB)
2025-05-26 12:05:29,201 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:29,208 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:29,208 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:29,208 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:29,222 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,223 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:29,223 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:29,234 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:29,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:29,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:29,247 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,254 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:29,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:29,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:29,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:29,256 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:29,258 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:29,259 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:29,259 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:29,259 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:29,260 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:29,369 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:29,380 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:29,380 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:29,380 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:29,396 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,396 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:29,396 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:05:29,407 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:29,407 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,409 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:29,425 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:29,444 - app.utils.memory_management - INFO - Memory before cleanup: 471.58 MB
2025-05-26 12:05:29,553 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:05:29,554 - app.utils.memory_management - INFO - Memory after cleanup: 471.58 MB (freed 0.00 MB)
2025-05-26 12:05:40,440 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:05:40,456 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:05:40,457 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:05:40,458 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:05:40,464 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:40,465 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:05:40,466 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:05:40,478 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:05:40,484 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:05:40,485 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:05:40,489 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:05:40,495 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:05:40,497 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:05:40,499 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:05:40,499 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:05:40,500 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:05:40,501 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:05:40,502 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:05:40,502 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:05:40,503 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:05:40,503 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:05:40,628 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:05:40,638 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:05:41,810 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:05:41,810 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:05:41,813 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.73
2025-05-26 12:05:41,813 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:05:41,813 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:05:41,813 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:05:58,038 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:06:00,175 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:06:00,182 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.25
2025-05-26 12:06:00,183 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.25
2025-05-26 12:06:00,183 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:06:00,183 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:06:00,183 - app.utils.error_handling - INFO - fetch_price executed in 18.37 seconds
2025-05-26 12:06:00,187 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:06:00,187 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:06:00,188 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:06:00,188 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:06:00,188 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:06:00,189 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:06:00,190 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:06:00,190 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:06:00,190 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:06:00,357 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:00,468 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:00,577 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:00,579 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-26 12:06:00,579 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:06:00,579 - models.predict - WARNING - Model file not found or import error for lstm with horizon 15: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:06:00,579 - models.predict - INFO - Skipping lstm model for horizon 15 - model not trained for this horizon
2025-05-26 12:06:00,603 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:00,711 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:00,819 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:00,821 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-05-26 12:06:00,822 - models.hybrid_model - INFO - XGBoost is available
2025-05-26 12:06:00,822 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-26 12:06:00,823 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-05-26 12:06:00,823 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_15min.joblib
2025-05-26 12:06:00,823 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_15min.joblib
2025-05-26 12:06:00,888 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-26 12:06:00,888 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-26 12:06:00,889 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:00,893 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:00,894 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7024726486206054
2025-05-26 12:06:00,894 - models.predict - INFO - Prediction for 15 minutes horizon: 80.19549945263381
2025-05-26 12:06:00,919 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:01,025 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:01,133 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:01,134 - models.predict - INFO - Using scikit-learn gb model for 15 minutes horizon
2025-05-26 12:06:01,135 - models.predict - INFO - Loading gb model for COMI with horizon 15
2025-05-26 12:06:01,135 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_15min.joblib
2025-05-26 12:06:01,136 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_15min.joblib
2025-05-26 12:06:01,138 - models.predict - WARNING - Model file not found or import error for gb with horizon 15: No module named '_loss'
2025-05-26 12:06:01,138 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:06:01,138 - models.predict - WARNING - Creating fallback model for gb due to version compatibility
2025-05-26 12:06:01,139 - models.predict - INFO - Using last known price for fallback: 80.25
2025-05-26 12:06:01,139 - models.predict - INFO - Fallback model created for gb
2025-05-26 12:06:01,157 - models.predict - INFO - Using fallback prediction for gb model
2025-05-26 12:06:01,157 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:01,158 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.996989557676922
2025-05-26 12:06:01,158 - models.predict - WARNING - Prediction 96.33502516705369 is too far from current price 80.25, using fallback
2025-05-26 12:06:01,158 - models.predict - INFO - Prediction for 15 minutes horizon: 79.17130907059452
2025-05-26 12:06:01,182 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:01,291 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:01,398 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:01,400 - models.predict - INFO - Using scikit-learn svr model for 15 minutes horizon
2025-05-26 12:06:01,400 - models.predict - INFO - Loading svr model for COMI with horizon 15
2025-05-26 12:06:01,400 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_15min.joblib
2025-05-26 12:06:01,400 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_15min.joblib
2025-05-26 12:06:01,402 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-26 12:06:01,403 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-26 12:06:01,403 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:01,406 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:01,406 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.49420578401451487
2025-05-26 12:06:01,407 - models.predict - INFO - Prediction for 15 minutes horizon: 68.78247590997243
2025-05-26 12:06:01,430 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:01,539 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:01,645 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:01,647 - models.predict - INFO - Using scikit-learn lr model for 15 minutes horizon
2025-05-26 12:06:01,647 - models.predict - INFO - Loading lr model for COMI with horizon 15
2025-05-26 12:06:01,647 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-26 12:06:01,647 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_15min.joblib
2025-05-26 12:06:01,648 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-26 12:06:01,648 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-26 12:06:01,648 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:01,655 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:01,656 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7097468281422202
2025-05-26 12:06:01,656 - models.predict - INFO - Prediction for 15 minutes horizon: 80.59412446814339
2025-05-26 12:06:01,658 - models.predict - INFO - Current price for COMI: 80.25
2025-05-26 12:06:01,659 - models.predict - INFO - Prophet prediction for 15 minutes: 80.22813136494285
2025-05-26 12:06:01,681 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:06:01,790 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:06:01,901 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:01,902 - models.predict - INFO - Using scikit-learn hybrid model for 15 minutes horizon
2025-05-26 12:06:01,903 - models.predict - INFO - Loading hybrid model for COMI with horizon 15
2025-05-26 12:06:01,904 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_15min.joblib
2025-05-26 12:06:01,904 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_15min.joblib, searching for alternatives...
2025-05-26 12:06:01,905 - models.sklearn_model - INFO - Found 6 potential model files: ['COMI_gb_15min.joblib', 'COMI_lr_15min.joblib', 'COMI_prophet_15min.joblib', 'COMI_rf_15min.joblib', 'COMI_svr_15min.joblib', 'COMI_xgb_15min.joblib']
2025-05-26 12:06:01,905 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_gb_15min.joblib
2025-05-26 12:06:01,906 - models.sklearn_model - INFO - Updated model type to gb
2025-05-26 12:06:01,906 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_15min.joblib
2025-05-26 12:06:01,908 - models.predict - WARNING - Model file not found or import error for hybrid with horizon 15: No module named '_loss'
2025-05-26 12:06:01,909 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:06:01,909 - models.predict - WARNING - Creating fallback model for hybrid due to version compatibility
2025-05-26 12:06:01,909 - models.predict - INFO - Using last known price for fallback: 80.25
2025-05-26 12:06:01,909 - models.predict - INFO - Fallback model created for hybrid
2025-05-26 12:06:01,909 - models.predict - INFO - Using fallback prediction for hybrid model
2025-05-26 12:06:01,910 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:01,910 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.9981479687900241
2025-05-26 12:06:01,910 - models.predict - WARNING - Prediction 96.3985060925044 is too far from current price 80.25, using fallback
2025-05-26 12:06:01,910 - models.predict - INFO - Prediction for 15 minutes horizon: 79.16301961811493
2025-05-26 12:06:01,932 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:02,046 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:02,155 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:02,156 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-26 12:06:02,156 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-26 12:06:02,813 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:06:03,807 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001F93AC98F70> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-26 12:06:03,810 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:06:03,810 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7038317322731018
2025-05-26 12:06:03,810 - models.predict - INFO - Prediction for 30 minutes horizon: 80.26997723262885
2025-05-26 12:06:03,832 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:03,960 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:04,066 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:04,067 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-26 12:06:04,068 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-26 12:06:04,068 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-26 12:06:04,068 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-26 12:06:04,125 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:06:04,125 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-26 12:06:04,125 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:04,130 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:04,130 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7060401630401612
2025-05-26 12:06:04,130 - models.predict - INFO - Prediction for 30 minutes horizon: 80.39099923190106
2025-05-26 12:06:04,153 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:04,259 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:04,369 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:04,370 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-05-26 12:06:04,370 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-05-26 12:06:04,370 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-05-26 12:06:04,371 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-05-26 12:06:04,373 - models.predict - WARNING - Model file not found or import error for gb with horizon 30: No module named '_loss'
2025-05-26 12:06:04,373 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:06:04,374 - models.predict - WARNING - Creating fallback model for gb due to version compatibility
2025-05-26 12:06:04,374 - models.predict - INFO - Using last known price for fallback: 80.25
2025-05-26 12:06:04,374 - models.predict - INFO - Fallback model created for gb
2025-05-26 12:06:04,391 - models.predict - INFO - Using fallback prediction for gb model
2025-05-26 12:06:04,391 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:04,391 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.9955748324364552
2025-05-26 12:06:04,392 - models.predict - INFO - Prediction for 30 minutes horizon: 96.25749822820826
2025-05-26 12:06:04,414 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:04,524 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:04,640 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:04,641 - models.predict - INFO - Using scikit-learn svr model for 30 minutes horizon
2025-05-26 12:06:04,642 - models.predict - INFO - Loading svr model for COMI with horizon 30
2025-05-26 12:06:04,642 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_30min.joblib
2025-05-26 12:06:04,642 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_30min.joblib
2025-05-26 12:06:04,643 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:06:04,644 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-26 12:06:04,644 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:04,644 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:04,645 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.49420578401451487
2025-05-26 12:06:04,645 - models.predict - INFO - Prediction for 30 minutes horizon: 68.78247590997243
2025-05-26 12:06:04,668 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:04,774 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:04,882 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:04,884 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-05-26 12:06:04,884 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-05-26 12:06:04,884 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_30min.joblib
2025-05-26 12:06:04,884 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_30min.joblib
2025-05-26 12:06:04,885 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:06:04,885 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-26 12:06:04,885 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:04,886 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:04,886 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7097468281422202
2025-05-26 12:06:04,886 - models.predict - INFO - Prediction for 30 minutes horizon: 80.59412446814339
2025-05-26 12:06:04,888 - models.predict - INFO - Current price for COMI: 80.25
2025-05-26 12:06:04,888 - models.predict - INFO - Prophet prediction for 30 minutes: 80.29119098410267
2025-05-26 12:06:04,915 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:06:05,021 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:06:05,126 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:05,128 - models.predict - INFO - Using scikit-learn hybrid model for 30 minutes horizon
2025-05-26 12:06:05,128 - models.predict - INFO - Loading hybrid model for COMI with horizon 30
2025-05-26 12:06:05,128 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_30min.joblib
2025-05-26 12:06:05,128 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-26 12:06:05,129 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-26 12:06:05,222 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:06:05,222 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-26 12:06:05,222 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:05,228 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:05,229 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7060401630401612
2025-05-26 12:06:05,230 - models.predict - INFO - Prediction for 30 minutes horizon: 80.39099923190106
2025-05-26 12:06:05,254 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:05,381 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:05,498 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:05,499 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-26 12:06:05,499 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:06:05,500 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:06:05,500 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-26 12:06:05,525 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:05,762 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:06,010 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:06,014 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-26 12:06:06,015 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-26 12:06:06,016 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-26 12:06:06,016 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-26 12:06:06,018 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-26 12:06:06,020 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-26 12:06:06,022 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-26 12:06:06,112 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-26 12:06:06,174 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-26 12:06:06,176 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-26 12:06:06,180 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:06,197 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:06,198 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.702253657579422
2025-05-26 12:06:06,205 - models.predict - INFO - Prediction for 60 minutes horizon: 80.18350043535233
2025-05-26 12:06:06,253 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:06,581 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:06,933 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.08s
2025-05-26 12:06:06,935 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-26 12:06:06,935 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-26 12:06:06,936 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-26 12:06:06,936 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-26 12:06:06,938 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-26 12:06:06,939 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-26 12:06:06,939 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-26 12:06:06,945 - models.predict - WARNING - Model file not found or import error for gb with horizon 60: No module named '_loss'
2025-05-26 12:06:06,945 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:06:06,947 - models.predict - WARNING - Creating fallback model for gb due to version compatibility
2025-05-26 12:06:06,948 - models.predict - INFO - Using last known price for fallback: 80.25
2025-05-26 12:06:06,948 - models.predict - INFO - Fallback model created for gb
2025-05-26 12:06:06,971 - models.predict - INFO - Using fallback prediction for gb model
2025-05-26 12:06:06,972 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:06,973 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.99847988788397
2025-05-26 12:06:06,973 - models.predict - WARNING - Prediction 96.41669785604155 is too far from current price 80.25, using fallback
2025-05-26 12:06:06,975 - models.predict - INFO - Prediction for 60 minutes horizon: 79.4889527043592
2025-05-26 12:06:07,020 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:07,224 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:07,559 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-05-26 12:06:07,562 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-26 12:06:07,562 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-26 12:06:07,562 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-26 12:06:07,563 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-26 12:06:07,564 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-26 12:06:07,564 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-26 12:06:07,564 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-26 12:06:07,565 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-26 12:06:07,568 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-26 12:06:07,573 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-26 12:06:07,577 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:07,577 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:07,590 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.49911912199468655
2025-05-26 12:06:07,611 - models.predict - INFO - Prediction for 60 minutes horizon: 69.05172788530882
2025-05-26 12:06:07,687 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:07,880 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:08,040 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:08,045 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-26 12:06:08,045 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-26 12:06:08,045 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-26 12:06:08,046 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-26 12:06:08,047 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-26 12:06:08,047 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-26 12:06:08,048 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-26 12:06:08,050 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-26 12:06:08,053 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-26 12:06:08,054 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-26 12:06:08,054 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:06:08,055 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:08,055 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.7322702210594918
2025-05-26 12:06:08,056 - models.predict - INFO - Prediction for 60 minutes horizon: 81.82840811406014
2025-05-26 12:06:08,062 - models.predict - INFO - Current price for COMI: 80.25
2025-05-26 12:06:08,063 - models.predict - INFO - Prophet prediction for 60 minutes: 80.22621549876348
2025-05-26 12:06:08,096 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:06:08,241 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:06:08,388 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:06:08,390 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-26 12:06:08,391 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-26 12:06:08,391 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-26 12:06:08,391 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-26 12:06:08,393 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-26 12:06:08,393 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-26 12:06:08,394 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-26 12:06:08,463 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-26 12:06:08,497 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-26 12:06:08,497 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-26 12:06:08,503 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:06:08,504 - models.predict - INFO - Current price: 80.25, Predicted scaled value: 0.702253657579422
2025-05-26 12:06:08,505 - models.predict - INFO - Prediction for 60 minutes horizon: 80.18350043535233
2025-05-26 12:06:08,523 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-26 12:06:08,540 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-26 12:06:08,565 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-26 12:06:08,615 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:06:12,237 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:12,237 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:12,237 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:12,246 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:12,247 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:12,248 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:06:12,253 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:06:12,253 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:06:12,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:12,262 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:06:12,262 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:06:12,263 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:06:12,264 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:06:12,268 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:12,268 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:06:12,268 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:06:12,269 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:06:12,269 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:06:12,273 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:12,273 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:06:12,274 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:12,274 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:12,274 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:12,279 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:12,280 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:12,288 - app.utils.memory_management - INFO - Memory before cleanup: 481.46 MB
2025-05-26 12:06:12,407 - app.utils.memory_management - INFO - Garbage collection: collected 329 objects
2025-05-26 12:06:12,408 - app.utils.memory_management - INFO - Memory after cleanup: 481.46 MB (freed 0.00 MB)
2025-05-26 12:06:31,897 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:06:31,936 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:31,939 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:31,940 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:31,945 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:31,947 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:31,950 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:06:31,982 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:06:31,988 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:06:31,989 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:06:31,993 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,004 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:06:32,011 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:06:32,015 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:06:32,020 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:06:32,020 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:06:32,022 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:06:32,023 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:06:32,023 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:06:32,023 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:06:32,023 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:06:32,192 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:06:32,221 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:32,225 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:32,227 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:32,232 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,234 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:32,234 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:06:32,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:06:32,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:06:32,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,252 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:06:32,252 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:06:32,253 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:06:32,253 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:06:32,261 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,266 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:06:32,267 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:06:32,268 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:06:32,268 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:06:32,272 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,273 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:06:32,273 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:32,273 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:32,274 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:32,278 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:32,278 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:32,283 - app.utils.memory_management - INFO - Memory before cleanup: 481.95 MB
2025-05-26 12:06:32,471 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:06:32,471 - app.utils.memory_management - INFO - Memory after cleanup: 481.95 MB (freed 0.00 MB)
2025-05-26 12:06:34,812 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:06:34,828 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:34,828 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:34,830 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:34,836 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:34,836 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:34,836 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:06:34,856 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:06:34,861 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:06:34,862 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:06:34,872 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:34,880 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:06:34,881 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:06:34,883 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:06:34,883 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:06:34,883 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:06:34,884 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:06:34,887 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:06:34,887 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:06:34,888 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:06:34,892 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:06:35,063 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:06:35,084 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:35,085 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:35,086 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:35,090 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:35,091 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:35,091 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:06:35,095 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:06:35,096 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:06:35,107 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:35,108 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:06:35,108 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:06:35,108 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:06:35,108 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:06:35,118 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:35,123 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:06:35,124 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:06:35,125 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:06:35,125 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:06:35,129 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:35,130 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:06:35,131 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:06:35,131 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:06:35,132 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:06:35,142 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:06:35,142 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:06:35,154 - app.utils.memory_management - INFO - Memory before cleanup: 482.21 MB
2025-05-26 12:06:35,305 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:06:35,306 - app.utils.memory_management - INFO - Memory after cleanup: 482.21 MB (freed 0.00 MB)
2025-05-26 12:07:03,382 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:03,400 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:03,402 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:03,402 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:03,406 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,407 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:03,407 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:03,418 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:03,428 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:03,429 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:03,434 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:03,438 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:03,438 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:03,566 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:03,579 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:03,604 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:03,604 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:03,608 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,612 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:03,613 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:03,613 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:03,613 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:03,618 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,618 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:03,618 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:03,619 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:03,619 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:03,623 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,623 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:03,623 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:03,624 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:03,624 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:03,628 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:03,630 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:03,631 - app.utils.memory_management - INFO - Memory before cleanup: 482.21 MB
2025-05-26 12:07:03,729 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:03,745 - app.utils.memory_management - INFO - Memory after cleanup: 482.21 MB (freed 0.00 MB)
2025-05-26 12:07:04,772 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:04,785 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:04,786 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:04,786 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:04,790 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,791 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:04,791 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:04,803 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:04,811 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:04,811 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:04,819 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:04,823 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:04,830 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:04,830 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:04,830 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:04,941 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:04,960 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:04,960 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:04,961 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:04,972 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,973 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:04,973 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:04,977 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:04,978 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:04,983 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:04,987 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:04,998 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:04,999 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:05,000 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:05,000 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:05,000 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:05,004 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:05,005 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:05,012 - app.utils.memory_management - INFO - Memory before cleanup: 482.21 MB
2025-05-26 12:07:05,123 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:05,124 - app.utils.memory_management - INFO - Memory after cleanup: 482.21 MB (freed 0.00 MB)
2025-05-26 12:07:06,039 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:06,055 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:06,055 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:06,056 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:06,062 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,063 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:06,063 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:06,075 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:06,081 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:06,081 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:06,088 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,088 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:06,088 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:06,088 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:06,088 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:06,099 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:06,100 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:06,101 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:06,101 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:06,102 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:06,102 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:06,209 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:06,222 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:06,222 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:06,222 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:06,232 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,232 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:06,232 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:06,232 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:06,232 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:06,249 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:06,265 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:06,265 - app.utils.memory_management - INFO - Memory before cleanup: 482.22 MB
2025-05-26 12:07:06,388 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:06,389 - app.utils.memory_management - INFO - Memory after cleanup: 482.22 MB (freed 0.00 MB)
2025-05-26 12:07:19,846 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:19,856 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:19,857 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:19,859 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:19,864 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:19,864 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:19,865 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:19,874 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:19,883 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:19,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:19,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:19,893 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:19,894 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:19,896 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:19,897 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:19,898 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:19,899 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:19,900 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:19,900 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:19,900 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:19,901 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:20,014 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:20,031 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:20,032 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:20,032 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:20,039 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,039 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,039 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:20,048 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:20,049 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:20,053 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,057 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:20,057 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:20,058 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:20,058 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:20,063 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,063 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,063 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:20,064 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:20,064 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:20,068 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,068 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,068 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:20,069 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:20,069 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:20,073 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,074 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,082 - app.utils.memory_management - INFO - Memory before cleanup: 482.22 MB
2025-05-26 12:07:20,195 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:20,195 - app.utils.memory_management - INFO - Memory after cleanup: 482.22 MB (freed 0.00 MB)
2025-05-26 12:07:20,756 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:20,767 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:20,768 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:20,768 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:20,775 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,776 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,776 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:20,788 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:20,798 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:20,799 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:20,803 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,803 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:20,803 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:20,803 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:20,803 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:20,812 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:20,814 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:20,815 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:20,816 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:20,816 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:20,816 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:20,925 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:20,967 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:20,980 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:20,989 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:20,994 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:20,994 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:20,994 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:20,999 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:21,000 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:21,004 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:21,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:21,008 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:21,008 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:21,008 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:21,014 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:21,015 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:21,015 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:21,016 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:21,016 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:21,021 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:21,022 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:21,023 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:21,023 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:21,023 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:21,036 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:21,037 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:21,044 - app.utils.memory_management - INFO - Memory before cleanup: 482.23 MB
2025-05-26 12:07:21,146 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:21,146 - app.utils.memory_management - INFO - Memory after cleanup: 482.23 MB (freed 0.00 MB)
2025-05-26 12:07:27,241 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:27,255 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:27,256 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:27,257 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:27,263 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,263 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:27,263 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:27,276 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:27,287 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:27,288 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:27,290 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:27,290 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:27,290 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:27,421 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:27,447 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:27,448 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:27,448 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:27,453 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:07:27,454 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:07:27,475 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,476 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:07:27,476 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:07:27,476 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:07:27,476 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:07:27,480 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,481 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:07:27,481 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:27,481 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:27,481 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:27,486 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:27,487 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:27,496 - app.utils.memory_management - INFO - Memory before cleanup: 482.23 MB
2025-05-26 12:07:27,606 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:07:27,607 - app.utils.memory_management - INFO - Memory after cleanup: 482.23 MB (freed 0.00 MB)
2025-05-26 12:07:44,743 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:07:44,759 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:07:44,760 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:07:44,760 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:07:44,766 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:44,766 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:07:44,767 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:07:44,778 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:07:44,789 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:07:44,789 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:07:44,793 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:07:44,797 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:07:44,797 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:07:44,798 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:07:44,798 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:07:44,798 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:07:44,799 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:07:44,800 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:07:44,803 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:07:44,803 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:07:44,804 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:07:44,907 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:07:45,053 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:07:46,238 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:07:46,238 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:07:46,238 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.6
2025-05-26 12:07:46,238 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:07:46,253 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:07:46,254 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:07:50,721 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:07:52,851 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:07:52,860 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.25
2025-05-26 12:07:52,860 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.25
2025-05-26 12:07:52,860 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:07:52,860 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:07:52,860 - app.utils.error_handling - INFO - fetch_price executed in 6.62 seconds
2025-05-26 12:07:52,861 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:07:54,962 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-05-26 12:07:54,963 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-05-26 12:07:54,963 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:07:54,996 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 79.98357166630628
2025-05-26 12:07:54,997 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-05-26 12:07:54,997 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:07:55,027 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:07:55,150 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:07:55,263 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:55,265 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-26 12:07:55,265 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-26 12:07:55,265 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-26 12:07:55,265 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-26 12:07:55,293 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:07:55,294 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-26 12:07:55,294 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:07:55,299 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:07:55,300 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 0.70072265625
2025-05-26 12:07:55,300 - models.predict - INFO - Prediction for 30 minutes horizon: 80.09959987608345
2025-05-26 12:07:55,326 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:07:55,437 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:07:55,546 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:55,547 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-05-26 12:07:55,548 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-05-26 12:07:55,548 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-05-26 12:07:55,548 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-05-26 12:07:55,552 - models.predict - WARNING - Model file not found or import error for gb with horizon 30: No module named '_loss'
2025-05-26 12:07:55,552 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:07:55,552 - models.predict - WARNING - Creating fallback model for gb due to version compatibility
2025-05-26 12:07:55,552 - models.predict - INFO - Using last known price for fallback: 79.9
2025-05-26 12:07:55,553 - models.predict - INFO - Fallback model created for gb
2025-05-26 12:07:55,568 - models.predict - INFO - Using fallback prediction for gb model
2025-05-26 12:07:55,569 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:07:55,569 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 1.006175127427625
2025-05-26 12:07:55,569 - models.predict - WARNING - Prediction 96.83839436126425 is too far from current price 79.9, using fallback
2025-05-26 12:07:55,570 - models.predict - INFO - Prediction for 30 minutes horizon: 80.48071848214957
2025-05-26 12:07:55,596 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:07:55,702 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:07:55,813 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:55,815 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-26 12:07:55,815 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-26 12:07:56,399 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:07:57,268 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:07:57,269 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 0.7124015688896179
2025-05-26 12:07:57,269 - models.predict - INFO - Prediction for 30 minutes horizon: 80.73960425297147
2025-05-26 12:07:57,270 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 80.43997420373483
2025-05-26 12:07:57,270 - app.models.adaptive - INFO - No valid models for COMI with 15min horizon, using equal weights
2025-05-26 12:07:57,271 - app.models.predict - INFO - Ensemble weights for COMI with 15min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:07:57,298 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:07:57,427 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:07:57,536 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:57,538 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-05-26 12:07:57,539 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-05-26 12:07:57,539 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_15min.joblib
2025-05-26 12:07:57,540 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_15min.joblib
2025-05-26 12:07:57,572 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-26 12:07:57,573 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-26 12:07:57,573 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-26 12:07:57,577 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:07:57,577 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 0.7006952714920044
2025-05-26 12:07:57,578 - models.predict - INFO - Prediction for 15 minutes horizon: 80.09809919142914
2025-05-26 12:07:57,608 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:07:57,714 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:07:57,825 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:57,826 - models.predict - INFO - Using scikit-learn gb model for 15 minutes horizon
2025-05-26 12:07:57,826 - models.predict - INFO - Loading gb model for COMI with horizon 15
2025-05-26 12:07:57,827 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_15min.joblib
2025-05-26 12:07:57,827 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_15min.joblib
2025-05-26 12:07:57,830 - models.predict - WARNING - Model file not found or import error for gb with horizon 15: No module named '_loss'
2025-05-26 12:07:57,831 - models.predict - WARNING - Detected scikit-learn version compatibility issue with gradient boosting model
2025-05-26 12:07:57,831 - models.predict - WARNING - Creating fallback model for gb due to version compatibility
2025-05-26 12:07:57,831 - models.predict - INFO - Using last known price for fallback: 79.9
2025-05-26 12:07:57,831 - models.predict - INFO - Fallback model created for gb
2025-05-26 12:07:57,848 - models.predict - INFO - Using fallback prediction for gb model
2025-05-26 12:07:57,848 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-26 12:07:57,848 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 1.0005114145935186
2025-05-26 12:07:57,849 - models.predict - WARNING - Prediction 96.52802291529858 is too far from current price 79.9, using fallback
2025-05-26 12:07:57,849 - models.predict - INFO - Prediction for 15 minutes horizon: 80.26838563562859
2025-05-26 12:07:57,865 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:07:57,983 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:07:58,095 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:58,096 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-26 12:07:58,097 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:07:58,097 - models.predict - WARNING - Model file not found or import error for lstm with horizon 15: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:07:58,098 - models.predict - INFO - Skipping lstm model for horizon 15 - model not trained for this horizon
2025-05-26 12:07:58,099 - app.models.predict - INFO - Adaptive ensemble prediction for 15min horizon: 80.18324241352886
2025-05-26 12:07:58,099 - app.models.predict - INFO - Prediction completed in 3.14 seconds
2025-05-26 12:07:58,099 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-05-26 12:07:58,100 - app.models.predict - INFO - Using specified model type: lstm
2025-05-26 12:07:58,127 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:07:58,233 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:07:58,344 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:58,346 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-26 12:07:58,346 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:07:58,347 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-26 12:07:58,347 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-26 12:07:58,347 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:07:58,449 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:07:58,572 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:07:58,573 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-26 12:07:58,573 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-26 12:07:59,151 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:08:00,066 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:08:00,067 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 0.7124015688896179
2025-05-26 12:08:00,068 - models.predict - INFO - Prediction for 30 minutes horizon: 80.73960425297147
2025-05-26 12:08:00,068 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:08:00,179 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:08:00,294 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:08:00,296 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-26 12:08:00,296 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:08:00,296 - models.predict - WARNING - Model file not found or import error for lstm with horizon 15: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-26 12:08:00,297 - models.predict - INFO - Skipping lstm model for horizon 15 - model not trained for this horizon
2025-05-26 12:08:00,298 - app.models.predict - INFO - Prediction completed in 2.20 seconds
2025-05-26 12:08:00,298 - app.models.predict - INFO - Using specified model type: bilstm
2025-05-26 12:08:00,326 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-26 12:08:00,451 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-26 12:08:00,594 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:08:00,595 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-05-26 12:08:00,595 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-26 12:08:00,596 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 60: Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-26 12:08:00,596 - models.predict - INFO - Skipping bilstm model for horizon 60 - model not trained for this horizon
2025-05-26 12:08:00,596 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-26 12:08:00,702 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-26 12:08:00,811 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:08:00,812 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-05-26 12:08:00,813 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-05-26 12:08:02,000 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-26 12:08:03,699 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-26 12:08:03,700 - models.predict - INFO - Current price: 79.9, Predicted scaled value: 0.7295126914978027
2025-05-26 12:08:03,700 - models.predict - INFO - Prediction for 30 minutes horizon: 81.67729371950252
2025-05-26 12:08:03,700 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-26 12:08:03,837 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-05-26 12:08:03,952 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:08:03,954 - models.predict - INFO - Loading bilstm model for COMI with horizon 15
2025-05-26 12:08:03,954 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_15min.keras or saved_models\COMI_bilstm_15min.h5
2025-05-26 12:08:03,955 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 15: Model not found at saved_models\COMI_bilstm_15min.keras or saved_models\COMI_bilstm_15min.h5
2025-05-26 12:08:03,955 - models.predict - INFO - Skipping bilstm model for horizon 15 - model not trained for this horizon
2025-05-26 12:08:03,957 - app.models.predict - INFO - Prediction completed in 3.66 seconds
2025-05-26 12:08:03,957 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-05-26 12:08:04,009 - app.components.enhanced_prediction - INFO - Trend analysis for COMI: down (strength: 0.07)
2025-05-26 12:08:04,035 - app.utils.performance - WARNING - No performance data found for COMI
2025-05-26 12:08:04,174 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:08:04,175 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:08:04,175 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:08:04,179 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:08:04,179 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:08:04,180 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:08:04,184 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:08:04,184 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:08:04,188 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:08:04,193 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:08:04,193 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:08:04,194 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:08:04,194 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:08:04,198 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:08:04,198 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:08:04,198 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:08:04,199 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:08:04,199 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:08:04,204 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:08:04,204 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:08:04,205 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:08:04,205 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:08:04,205 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:08:04,209 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:08:04,210 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:08:04,216 - app.utils.memory_management - INFO - Memory before cleanup: 529.19 MB
2025-05-26 12:08:04,363 - app.utils.memory_management - INFO - Garbage collection: collected 20266 objects
2025-05-26 12:08:04,364 - app.utils.memory_management - INFO - Memory after cleanup: 488.99 MB (freed 40.20 MB)
2025-05-26 12:09:42,623 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:09:42,657 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:09:42,658 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:09:42,658 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:09:42,663 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:42,664 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:09:42,664 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:09:42,682 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:09:42,691 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:09:42,691 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:09:42,704 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:42,710 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:09:42,710 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:09:42,711 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:09:42,722 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:09:42,724 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:09:42,725 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:09:42,726 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:09:42,728 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:09:42,728 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:09:42,728 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:09:42,991 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:09:43,028 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:09:43,028 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:09:43,029 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:09:43,036 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:43,040 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:09:43,041 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:09:43,045 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:09:43,054 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:09:43,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:43,064 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:09:43,065 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:09:43,071 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:09:43,072 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:09:43,076 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:43,076 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:09:43,077 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:09:43,077 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:09:43,077 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:09:43,082 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:43,083 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:09:43,085 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:09:43,086 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:09:43,086 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:09:43,091 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:43,092 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:09:43,106 - app.utils.memory_management - INFO - Memory before cleanup: 489.70 MB
2025-05-26 12:09:43,283 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:09:43,283 - app.utils.memory_management - INFO - Memory after cleanup: 489.70 MB (freed 0.00 MB)
2025-05-26 12:09:49,010 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:09:49,021 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:09:49,022 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:09:49,023 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:09:49,028 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:49,028 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:09:49,029 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:09:49,045 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:09:49,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:09:49,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:09:49,061 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:09:49,065 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:09:49,065 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:09:49,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:09:49,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:09:49,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:09:49,067 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:09:49,067 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:09:49,068 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:09:49,068 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:09:49,068 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:09:49,209 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:09:49,360 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:09:50,448 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:09:50,449 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:09:50,451 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.05
2025-05-26 12:09:50,452 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:09:50,452 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:09:50,452 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:09:59,223 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:10:01,355 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:10:01,363 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.25
2025-05-26 12:10:01,364 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.25
2025-05-26 12:10:01,364 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:10:01,365 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:10:01,366 - app.utils.error_handling - INFO - fetch_price executed in 10.91 seconds
2025-05-26 12:10:01,367 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:10:03,518 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-05-26 12:10:03,519 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-05-26 12:10:03,519 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:10:03,550 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 79.98357166630628
2025-05-26 12:10:03,551 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-05-26 12:10:03,552 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:10:03,591 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 80.43997420373483
2025-05-26 12:10:03,592 - app.models.adaptive - INFO - No valid models for COMI with 15min horizon, using equal weights
2025-05-26 12:10:03,593 - app.models.predict - INFO - Ensemble weights for COMI with 15min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-26 12:10:03,637 - app.models.predict - INFO - Adaptive ensemble prediction for 15min horizon: 80.18324241352886
2025-05-26 12:10:03,637 - app.models.predict - INFO - Prediction completed in 0.12 seconds
2025-05-26 12:10:03,637 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-05-26 12:10:03,637 - app.models.predict - INFO - Using specified model type: lstm
2025-05-26 12:10:03,670 - app.models.predict - INFO - Prediction completed in 0.03 seconds
2025-05-26 12:10:03,670 - app.models.predict - INFO - Using specified model type: bilstm
2025-05-26 12:10:03,670 - app.models.predict - INFO - Prediction completed in 0.00 seconds
2025-05-26 12:10:03,685 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-05-26 12:10:03,686 - app.components.enhanced_prediction - INFO - Trend analysis for COMI: down (strength: 0.07)
2025-05-26 12:10:03,727 - app.utils.performance - WARNING - No performance data found for COMI
2025-05-26 12:10:03,797 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:10:03,798 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:10:03,800 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:10:03,805 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:10:03,805 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:10:03,805 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:10:03,824 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:10:03,829 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:10:03,835 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:10:03,837 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:10:03,862 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:10:03,863 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:10:03,864 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:10:03,865 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:10:03,866 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:10:03,888 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:10:03,888 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:10:03,906 - app.utils.memory_management - INFO - Memory before cleanup: 484.09 MB
2025-05-26 12:10:04,132 - app.utils.memory_management - INFO - Garbage collection: collected 822 objects
2025-05-26 12:10:04,133 - app.utils.memory_management - INFO - Memory after cleanup: 484.09 MB (freed 0.00 MB)
2025-05-26 12:10:58,561 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:10:58,602 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 12:10:58,665 - app.components.performance_metrics - INFO - Target time 2025-05-24 21:46:55.900507 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,670 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-26 12:10:58,675 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-26 12:10:58,679 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-26 12:10:58,683 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:37:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,687 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:47:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,693 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:02:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,696 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:32:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,698 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:49:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,710 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:04:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,728 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:34:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,764 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:31:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,780 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:41:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,782 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:56:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,791 - app.components.performance_metrics - INFO - Target time 2025-05-25 13:26:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,796 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:42:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,801 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:57:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,813 - app.components.performance_metrics - INFO - Target time 2025-05-25 13:27:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,818 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:33:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,823 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:43:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,828 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:58:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,833 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:28:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,839 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:44:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,845 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:29:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,849 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:59:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,854 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:36:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,859 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:46:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,863 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:01:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,867 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:31:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,871 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:47:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,876 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:32:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,878 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:02:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:58,878 - app.components.performance_metrics - INFO - Target time 2025-05-26 12:08:50.943741 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:10:59,040 - app.utils.memory_management - INFO - Memory before cleanup: 486.43 MB
2025-05-26 12:10:59,160 - app.utils.memory_management - INFO - Garbage collection: collected 1663 objects
2025-05-26 12:10:59,160 - app.utils.memory_management - INFO - Memory after cleanup: 486.41 MB (freed 0.02 MB)
2025-05-26 12:13:06,087 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:06,124 - app.components.performance_metrics - INFO - Target time 2025-05-24 21:46:55.900507 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,130 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-26 12:13:06,133 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-26 12:13:06,133 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-26 12:13:06,148 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:37:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,153 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:47:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,157 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:02:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,161 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:32:50.090856 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,166 - app.components.performance_metrics - INFO - Target time 2025-05-24 16:49:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,170 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:04:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,175 - app.components.performance_metrics - INFO - Target time 2025-05-24 17:34:09.144682 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,184 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:31:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,189 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:41:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,195 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:56:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,201 - app.components.performance_metrics - INFO - Target time 2025-05-25 13:26:13.900954 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,205 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:42:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,209 - app.components.performance_metrics - INFO - Target time 2025-05-25 12:57:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,214 - app.components.performance_metrics - INFO - Target time 2025-05-25 13:27:31.833735 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,218 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:33:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,223 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:43:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,227 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:58:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,233 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:28:50.168589 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,238 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:44:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,242 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:29:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,246 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:59:53.942793 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,252 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:36:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,256 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:46:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,256 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:01:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,299 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:31:20.732401 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,313 - app.components.performance_metrics - INFO - Target time 2025-05-25 14:47:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,313 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:32:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,313 - app.components.performance_metrics - INFO - Target time 2025-05-25 15:02:14.732106 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,334 - app.components.performance_metrics - INFO - Target time 2025-05-26 12:08:50.943741 is in the future compared to latest data 2025-05-22 00:00:00
2025-05-26 12:13:06,476 - app.utils.memory_management - INFO - Memory before cleanup: 487.16 MB
2025-05-26 12:13:06,614 - app.utils.memory_management - INFO - Garbage collection: collected 1561 objects
2025-05-26 12:13:06,614 - app.utils.memory_management - INFO - Memory after cleanup: 487.16 MB (freed 0.00 MB)
2025-05-26 12:13:07,514 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:07,516 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:13:07,516 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:13:07,516 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:13:07,516 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:13:07,536 - app.utils.memory_management - INFO - Memory before cleanup: 487.15 MB
2025-05-26 12:13:07,647 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-05-26 12:13:07,647 - app.utils.memory_management - INFO - Memory after cleanup: 487.15 MB (freed 0.00 MB)
2025-05-26 12:13:24,324 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:24,335 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:13:24,336 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:13:24,336 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:13:24,337 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:13:24,348 - app.utils.memory_management - INFO - Memory before cleanup: 487.35 MB
2025-05-26 12:13:24,480 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-26 12:13:24,480 - app.utils.memory_management - INFO - Memory after cleanup: 487.35 MB (freed 0.00 MB)
2025-05-26 12:13:30,041 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:30,054 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:13:30,054 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:13:30,054 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:13:30,055 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:13:30,069 - app.utils.backtesting - INFO - Created model instance of type rf
2025-05-26 12:13:30,069 - app.utils.backtesting - INFO - Loading model for COMI with horizon 7
2025-05-26 12:13:30,070 - app.utils.backtesting - INFO - Looking in path: saved_models
2025-05-26 12:13:30,072 - app.utils.backtesting - INFO - Found matching model files: ['COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler_7200min.joblib']
2025-05-26 12:13:30,073 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_7min.joblib
2025-05-26 12:13:30,074 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_7min.joblib
2025-05-26 12:13:30,128 - app.utils.backtesting - INFO - Model loaded successfully: RandomForestRegressor
2025-05-26 12:13:30,128 - app.utils.backtesting - INFO - Starting backtest with test period: 30 days
2025-05-26 12:13:30,128 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-05-26 12:13:30,145 - app.utils.backtesting - INFO - Added technical indicators. New shape: (576, 21)
2025-05-26 12:13:30,163 - app.utils.backtesting - INFO - Prepared features. Final shape: (576, 36)
2025-05-26 12:13:30,163 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 5 features
2025-05-26 12:13:30,163 - app.utils.backtesting - INFO - Model type detection: sklearn=True, hybrid=False
2025-05-26 12:13:30,163 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,163 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,170 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,171 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,173 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,173 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,174 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,175 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,176 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,177 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,178 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,179 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,180 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,180 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,182 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,182 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,184 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,184 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,188 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,189 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,191 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,192 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,192 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,192 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,192 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,192 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,192 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,202 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,202 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:30,219 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-05-26 12:13:30,219 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-05-26 12:13:30,219 - app.utils.backtesting - INFO - Backtest completed with 5 results
2025-05-26 12:13:30,270 - app.utils.memory_management - INFO - Memory before cleanup: 492.62 MB
2025-05-26 12:13:30,370 - app.utils.memory_management - INFO - Garbage collection: collected 602 objects
2025-05-26 12:13:30,370 - app.utils.memory_management - INFO - Memory after cleanup: 492.62 MB (freed 0.00 MB)
2025-05-26 12:13:43,220 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:43,234 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:13:43,235 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:13:43,235 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:13:43,236 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:13:43,244 - app.utils.memory_management - INFO - Memory before cleanup: 492.59 MB
2025-05-26 12:13:43,349 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-26 12:13:43,349 - app.utils.memory_management - INFO - Memory after cleanup: 492.59 MB (freed 0.00 MB)
2025-05-26 12:13:56,144 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:13:56,144 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:13:56,144 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:13:56,144 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:13:56,160 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:13:56,172 - app.utils.backtesting - INFO - Created model instance of type rf
2025-05-26 12:13:56,173 - app.utils.backtesting - INFO - Loading model for COMI with horizon 7
2025-05-26 12:13:56,174 - app.utils.backtesting - INFO - Looking in path: saved_models
2025-05-26 12:13:56,176 - app.utils.backtesting - INFO - Found matching model files: ['COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler_7200min.joblib']
2025-05-26 12:13:56,178 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_7min.joblib
2025-05-26 12:13:56,179 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_7min.joblib
2025-05-26 12:13:56,208 - app.utils.backtesting - INFO - Model loaded successfully: RandomForestRegressor
2025-05-26 12:13:56,209 - app.utils.backtesting - INFO - Starting backtest with test period: 30 days
2025-05-26 12:13:56,211 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-05-26 12:13:56,212 - app.utils.backtesting - INFO - Added technical indicators. New shape: (576, 21)
2025-05-26 12:13:56,249 - app.utils.backtesting - INFO - Prepared features. Final shape: (576, 36)
2025-05-26 12:13:56,250 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 5 features
2025-05-26 12:13:56,250 - app.utils.backtesting - INFO - Model type detection: sklearn=True, hybrid=False
2025-05-26 12:13:56,251 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,252 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,253 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,254 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,254 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,254 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,254 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,254 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,254 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,254 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,263 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,263 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,265 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,265 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,267 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,267 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,269 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,269 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,271 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,271 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,273 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,273 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,275 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,275 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,278 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,279 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,281 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,282 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,282 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,282 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,282 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,282 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,282 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,294 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,295 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,297 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,297 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,299 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,299 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,301 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,301 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,303 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,303 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,305 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,305 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,305 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,305 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,305 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,305 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,312 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,312 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,312 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,312 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,312 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-26 12:13:56,312 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-26 12:13:56,312 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-05-26 12:13:56,312 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-05-26 12:13:56,312 - app.utils.backtesting - INFO - Backtest completed with 5 results
2025-05-26 12:13:56,351 - app.utils.memory_management - INFO - Memory before cleanup: 492.62 MB
2025-05-26 12:13:56,528 - app.utils.memory_management - INFO - Garbage collection: collected 1083 objects
2025-05-26 12:13:56,528 - app.utils.memory_management - INFO - Memory after cleanup: 492.62 MB (freed 0.00 MB)
2025-05-26 12:14:02,420 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:14:02,420 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-26 12:14:02,433 - app.utils.backtesting - INFO - Historical data shape: (576, 6)
2025-05-26 12:14:02,433 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-05-26 12:14:02,434 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-22 00:00:00
2025-05-26 12:14:02,444 - app.utils.memory_management - INFO - Memory before cleanup: 492.60 MB
2025-05-26 12:14:02,584 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-26 12:14:02,584 - app.utils.memory_management - INFO - Memory after cleanup: 492.60 MB (freed 0.00 MB)
2025-05-26 12:14:07,596 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:14:07,615 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:14:07,616 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:14:07,616 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:14:07,617 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:14:08,758 - app.utils.memory_management - INFO - Memory before cleanup: 496.24 MB
2025-05-26 12:14:08,886 - app.utils.memory_management - INFO - Garbage collection: collected 2287 objects
2025-05-26 12:14:08,887 - app.utils.memory_management - INFO - Memory after cleanup: 496.24 MB (freed 0.00 MB)
2025-05-26 12:14:28,442 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:14:28,462 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:14:28,463 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:14:28,463 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:14:28,464 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:14:28,719 - app.utils.memory_management - INFO - Memory before cleanup: 498.10 MB
2025-05-26 12:14:28,859 - app.utils.memory_management - INFO - Garbage collection: collected 2413 objects
2025-05-26 12:14:28,859 - app.utils.memory_management - INFO - Memory after cleanup: 498.10 MB (freed 0.00 MB)
2025-05-26 12:14:29,306 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:14:29,326 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:14:29,338 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:14:29,338 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:14:29,338 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:14:29,338 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:14:29,366 - app.utils.memory_management - INFO - Memory before cleanup: 498.04 MB
2025-05-26 12:14:29,477 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-26 12:14:29,477 - app.utils.memory_management - INFO - Memory after cleanup: 498.04 MB (freed 0.00 MB)
2025-05-26 12:14:59,130 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:14:59,159 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:14:59,171 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:14:59,172 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:14:59,173 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:14:59,173 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:14:59,201 - app.utils.memory_management - INFO - Memory before cleanup: 498.05 MB
2025-05-26 12:14:59,324 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-05-26 12:14:59,324 - app.utils.memory_management - INFO - Memory after cleanup: 498.05 MB (freed 0.00 MB)
2025-05-26 12:15:03,931 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:15:03,956 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:15:03,970 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:15:03,972 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:15:03,972 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:15:03,972 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:15:04,093 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:15:05,292 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:15:05,295 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:15:05,303 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.62
2025-05-26 12:15:05,304 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:15:05,304 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:15:05,305 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:15:10,182 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:15:12,312 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:15:12,322 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.2
2025-05-26 12:15:12,322 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.2
2025-05-26 12:15:12,322 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:15:12,322 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:15:12,322 - app.utils.error_handling - INFO - fetch_price executed in 7.02 seconds
2025-05-26 12:15:12,323 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:15:14,502 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:15:14,620 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:15:14,795 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.05s
2025-05-26 12:15:14,796 - models.predict - INFO - Using EnsembleModel for 240 minutes horizon
2025-05-26 12:15:14,797 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-05-26 12:15:14,797 - models.predict - ERROR - Error making predictions: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:15:14,797 - app.components.tradingview_predictions - WARNING - Error with prediction: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:15:14,798 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-26 12:15:14,798 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 79.9
2025-05-26 12:15:14,798 - app.components.tradingview_predictions - INFO - Fallback prediction for 240 minutes: 80.04884456633646
2025-05-26 12:15:14,844 - app.utils.memory_management - INFO - Memory before cleanup: 498.12 MB
2025-05-26 12:15:14,970 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-05-26 12:15:14,971 - app.utils.memory_management - INFO - Memory after cleanup: 498.12 MB (freed 0.00 MB)
2025-05-26 12:16:33,498 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:16:33,505 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 12:16:33,549 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:16:33,583 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:16:33,584 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:16:33,585 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:16:33,585 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:16:33,632 - app.utils.memory_management - INFO - Memory before cleanup: 498.11 MB
2025-05-26 12:16:33,850 - app.utils.memory_management - INFO - Garbage collection: collected 300 objects
2025-05-26 12:16:33,850 - app.utils.memory_management - INFO - Memory after cleanup: 498.11 MB (freed 0.00 MB)
2025-05-26 12:17:15,445 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:17:15,490 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:17:15,505 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:17:15,505 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:17:15,505 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:17:15,505 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:17:15,551 - app.utils.memory_management - INFO - Memory before cleanup: 498.13 MB
2025-05-26 12:17:15,735 - app.utils.memory_management - INFO - Garbage collection: collected 293 objects
2025-05-26 12:17:15,736 - app.utils.memory_management - INFO - Memory after cleanup: 498.13 MB (freed 0.00 MB)
2025-05-26 12:17:36,310 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:17:36,337 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:17:36,353 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:17:36,353 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:17:36,354 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:17:36,355 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:17:36,383 - app.utils.memory_management - INFO - Memory before cleanup: 498.14 MB
2025-05-26 12:17:36,498 - app.utils.memory_management - INFO - Garbage collection: collected 290 objects
2025-05-26 12:17:36,498 - app.utils.memory_management - INFO - Memory after cleanup: 498.14 MB (freed 0.00 MB)
2025-05-26 12:17:39,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:17:39,359 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:17:39,373 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:17:39,373 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:17:39,377 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:17:39,378 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:17:39,494 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:17:40,710 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:17:40,711 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:17:40,715 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.85
2025-05-26 12:17:40,715 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:17:40,716 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:17:40,716 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:17:46,141 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:17:48,324 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:17:48,333 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.3
2025-05-26 12:17:48,333 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.3
2025-05-26 12:17:48,333 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:17:48,333 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:17:48,333 - app.utils.error_handling - INFO - fetch_price executed in 7.62 seconds
2025-05-26 12:17:48,335 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:17:50,496 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:17:50,604 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:17:50,721 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:17:50,721 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-05-26 12:17:50,721 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-05-26 12:17:50,721 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-05-26 12:17:50,721 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-05-26 12:17:50,721 - app.components.tradingview_predictions - WARNING - No valid predictions generated for LSTM - skipping this model
2025-05-26 12:17:50,756 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:17:50,873 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:17:50,992 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:17:50,993 - models.predict - INFO - Loading bilstm model for COMI with horizon 240
2025-05-26 12:17:50,994 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-05-26 12:17:50,994 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 240: Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-05-26 12:17:50,994 - models.predict - INFO - Skipping bilstm model for horizon 240 - model not trained for this horizon
2025-05-26 12:17:50,995 - app.components.tradingview_predictions - WARNING - No valid predictions generated for BiLSTM - skipping this model
2025-05-26 12:17:51,004 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:17:51,132 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:17:51,244 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:17:51,246 - models.predict - INFO - Using scikit-learn rf model for 240 minutes horizon
2025-05-26 12:17:51,246 - models.predict - INFO - Loading rf model for COMI with horizon 240
2025-05-26 12:17:51,246 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_240min.joblib
2025-05-26 12:17:51,247 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_240min.joblib, searching for alternatives...
2025-05-26 12:17:51,248 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-05-26 12:17:51,249 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-05-26 12:17:51,251 - models.predict - WARNING - Model file not found or import error for rf with horizon 240: No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-05-26 12:17:51,253 - models.predict - INFO - Skipping rf model for horizon 240 - model not trained for this horizon
2025-05-26 12:17:51,254 - app.components.tradingview_predictions - WARNING - No valid predictions generated for RandomForest - skipping this model
2025-05-26 12:17:51,274 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:17:51,388 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:17:51,507 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:17:51,508 - models.predict - INFO - Using scikit-learn gb model for 240 minutes horizon
2025-05-26 12:17:51,508 - models.predict - INFO - Loading gb model for COMI with horizon 240
2025-05-26 12:17:51,509 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_240min.joblib
2025-05-26 12:17:51,509 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_240min.joblib, searching for alternatives...
2025-05-26 12:17:51,510 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-05-26 12:17:51,512 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-05-26 12:17:51,514 - models.predict - WARNING - Model file not found or import error for gb with horizon 240: No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-05-26 12:17:51,516 - models.predict - INFO - Skipping gb model for horizon 240 - model not trained for this horizon
2025-05-26 12:17:51,516 - app.components.tradingview_predictions - WARNING - No valid predictions generated for GradientBoosting - skipping this model
2025-05-26 12:17:51,535 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:17:51,649 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:17:51,765 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:17:51,767 - models.predict - INFO - Using EnsembleModel for 240 minutes horizon
2025-05-26 12:17:51,767 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-05-26 12:17:51,768 - models.predict - ERROR - Error making predictions: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:17:51,768 - app.components.tradingview_predictions - WARNING - Error with prediction: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:17:51,768 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-26 12:17:51,769 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 79.9
2025-05-26 12:17:51,769 - app.components.tradingview_predictions - INFO - Fallback prediction for 240 minutes: 79.74907878375478
2025-05-26 12:17:51,769 - app.components.tradingview_predictions - INFO - Successfully generated 1 predictions for Ensemble
2025-05-26 12:17:51,811 - app.utils.memory_management - INFO - Memory before cleanup: 498.23 MB
2025-05-26 12:17:51,939 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-26 12:17:51,939 - app.utils.memory_management - INFO - Memory after cleanup: 498.23 MB (freed 0.00 MB)
2025-05-26 12:18:49,260 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:18:49,310 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:18:49,330 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:18:49,330 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:18:49,330 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:18:49,330 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:18:49,438 - app.utils.memory_management - INFO - Memory before cleanup: 498.21 MB
2025-05-26 12:18:49,595 - app.utils.memory_management - INFO - Garbage collection: collected 318 objects
2025-05-26 12:18:49,595 - app.utils.memory_management - INFO - Memory after cleanup: 498.21 MB (freed 0.00 MB)
2025-05-26 12:18:50,099 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:18:50,164 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:18:50,266 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:18:50,268 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:18:50,269 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:18:50,269 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:18:50,338 - app.utils.memory_management - INFO - Memory before cleanup: 498.21 MB
2025-05-26 12:18:50,484 - app.utils.memory_management - INFO - Garbage collection: collected 290 objects
2025-05-26 12:18:50,486 - app.utils.memory_management - INFO - Memory after cleanup: 498.21 MB (freed 0.00 MB)
2025-05-26 12:18:51,081 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:18:51,109 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:18:51,132 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:18:51,133 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:18:51,133 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:18:51,134 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:18:51,165 - app.utils.memory_management - INFO - Memory before cleanup: 498.21 MB
2025-05-26 12:18:51,310 - app.utils.memory_management - INFO - Garbage collection: collected 290 objects
2025-05-26 12:18:51,310 - app.utils.memory_management - INFO - Memory after cleanup: 498.21 MB (freed 0.00 MB)
2025-05-26 12:18:57,378 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:18:57,406 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:18:57,423 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:18:57,424 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:18:57,425 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:18:57,426 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:18:57,537 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-26 12:18:58,598 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-26 12:18:58,598 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-26 12:18:58,601 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.17
2025-05-26 12:18:58,601 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-26 12:18:58,601 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-26 12:18:58,601 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-26 12:19:03,164 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-26 12:19:05,315 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-26 12:19:05,324 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.3
2025-05-26 12:19:05,324 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.3
2025-05-26 12:19:05,324 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-26 12:19:05,325 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-26 12:19:05,325 - app.utils.error_handling - INFO - fetch_price executed in 6.72 seconds
2025-05-26 12:19:05,326 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-26 12:19:07,470 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:19:07,579 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:19:07,695 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:19:07,695 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-05-26 12:19:07,695 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-05-26 12:19:07,695 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-05-26 12:19:07,695 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-05-26 12:19:07,695 - app.components.tradingview_predictions - WARNING - No valid predictions generated for LSTM - skipping this model
2025-05-26 12:19:07,713 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-05-26 12:19:07,839 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-05-26 12:19:07,955 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-26 12:19:07,956 - models.predict - INFO - Using EnsembleModel for 240 minutes horizon
2025-05-26 12:19:07,957 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-05-26 12:19:07,958 - models.predict - ERROR - Error making predictions: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:19:07,958 - app.components.tradingview_predictions - WARNING - Error with prediction: EnsembleModel.load() got an unexpected keyword argument 'symbol'
2025-05-26 12:19:07,958 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-26 12:19:07,958 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 79.9
2025-05-26 12:19:07,959 - app.components.tradingview_predictions - INFO - Fallback prediction for 240 minutes: 80.08718747763244
2025-05-26 12:19:07,959 - app.components.tradingview_predictions - INFO - Successfully generated 1 predictions for Ensemble
2025-05-26 12:19:08,003 - app.utils.memory_management - INFO - Memory before cleanup: 498.26 MB
2025-05-26 12:19:08,115 - app.utils.memory_management - INFO - Garbage collection: collected 206 objects
2025-05-26 12:19:08,116 - app.utils.memory_management - INFO - Memory after cleanup: 498.26 MB (freed 0.00 MB)
2025-05-26 12:19:56,909 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:19:56,957 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:19:56,984 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:19:56,985 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:19:56,986 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:19:56,986 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:19:57,026 - app.utils.memory_management - INFO - Memory before cleanup: 498.23 MB
2025-05-26 12:19:57,169 - app.utils.memory_management - INFO - Garbage collection: collected 318 objects
2025-05-26 12:19:57,170 - app.utils.memory_management - INFO - Memory after cleanup: 498.23 MB (freed 0.00 MB)
2025-05-26 12:20:48,863 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:20:48,918 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:20:48,944 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:20:48,945 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:20:48,945 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:20:48,946 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:20:49,039 - app.utils.memory_management - INFO - Memory before cleanup: 498.21 MB
2025-05-26 12:20:49,197 - app.utils.memory_management - INFO - Garbage collection: collected 292 objects
2025-05-26 12:20:49,197 - app.utils.memory_management - INFO - Memory after cleanup: 498.21 MB (freed 0.00 MB)
2025-05-26 12:21:13,574 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:21:13,596 - app.utils.memory_management - INFO - Memory before cleanup: 498.24 MB
2025-05-26 12:21:13,771 - app.utils.memory_management - INFO - Garbage collection: collected 292 objects
2025-05-26 12:21:13,771 - app.utils.memory_management - INFO - Memory after cleanup: 498.24 MB (freed 0.00 MB)
2025-05-26 12:21:13,884 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:21:13,919 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:21:13,932 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-26 12:21:13,933 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:21:13,933 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:21:13,935 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:21:13,960 - app.utils.memory_management - INFO - Memory before cleanup: 498.25 MB
2025-05-26 12:21:14,118 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-26 12:21:14,118 - app.utils.memory_management - INFO - Memory after cleanup: 498.23 MB (freed 0.02 MB)
2025-05-26 12:21:21,042 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:21:21,070 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-26 12:21:21,089 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 12:21:21,091 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 12:21:21,093 - app.utils.common - INFO - Data shape: (576, 6)
2025-05-26 12:21:21,094 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-26 12:21:21,133 - app.utils.memory_management - INFO - Memory before cleanup: 498.23 MB
2025-05-26 12:21:21,258 - app.utils.memory_management - INFO - Garbage collection: collected 282 objects
2025-05-26 12:21:21,269 - app.utils.memory_management - INFO - Memory after cleanup: 498.23 MB (freed 0.00 MB)
2025-05-26 12:21:40,777 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:21:40,779 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 12:21:40,794 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:21:40,795 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:21:40,795 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:21:40,799 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:40,799 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:21:40,800 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:21:40,812 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:21:40,820 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:21:40,821 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:21:40,826 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:21:40,827 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:21:40,827 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:21:40,959 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:21:40,981 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:21:40,981 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:21:40,982 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:21:40,991 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:40,993 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:21:40,993 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:21:40,997 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:21:40,998 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:21:41,002 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:41,006 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:21:41,006 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:21:41,007 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:21:41,007 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:21:41,011 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:41,011 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:21:41,011 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:21:41,012 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:21:41,012 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:21:41,017 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:41,017 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:21:41,017 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:21:41,018 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:21:41,018 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:21:41,021 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:21:41,021 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:21:41,030 - app.utils.memory_management - INFO - Memory before cleanup: 498.25 MB
2025-05-26 12:21:41,150 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:21:41,150 - app.utils.memory_management - INFO - Memory after cleanup: 498.25 MB (freed 0.00 MB)
2025-05-26 12:22:14,622 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:22:14,642 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:14,645 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:14,646 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:14,653 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,654 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:14,654 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:22:14,676 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:22:14,685 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:14,685 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:14,690 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,697 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:22:14,697 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:22:14,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:22:14,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:22:14,699 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:22:14,699 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:22:14,701 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:22:14,701 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:22:14,702 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:22:14,702 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:22:14,865 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:22:14,895 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:14,895 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:14,896 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:14,900 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,901 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:14,901 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:14,905 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:14,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:14,912 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,920 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:22:14,920 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:22:14,921 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:22:14,921 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:22:14,925 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,925 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:22:14,925 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:22:14,926 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:22:14,926 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:22:14,930 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,931 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:22:14,931 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:14,931 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:14,931 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:14,935 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:14,936 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:14,943 - app.utils.memory_management - INFO - Memory before cleanup: 492.26 MB
2025-05-26 12:22:15,066 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:22:15,066 - app.utils.memory_management - INFO - Memory after cleanup: 492.26 MB (freed 0.00 MB)
2025-05-26 12:22:24,324 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:22:24,340 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:24,340 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:24,341 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:24,347 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,347 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:24,348 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:22:24,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:24,366 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:22:24,375 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:24,376 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:24,380 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,383 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:22:24,383 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:22:24,383 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:22:24,386 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:22:24,387 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:22:24,388 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:22:24,389 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:22:24,390 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:22:24,390 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:22:24,390 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:22:24,504 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:22:24,529 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:24,529 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:24,530 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:24,535 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,535 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:24,536 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:24,540 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:24,540 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:24,544 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,548 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:22:24,549 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:22:24,550 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:22:24,550 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:22:24,560 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,560 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:22:24,560 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:22:24,561 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:22:24,561 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:22:24,565 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,565 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:22:24,566 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:24,567 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:24,567 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:24,571 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:24,571 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:24,578 - app.utils.memory_management - INFO - Memory before cleanup: 492.29 MB
2025-05-26 12:22:24,709 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:22:24,732 - app.utils.memory_management - INFO - Memory after cleanup: 492.29 MB (freed 0.00 MB)
2025-05-26 12:22:42,285 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:22:42,293 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:42,293 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:42,293 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:42,305 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,306 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:42,306 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:22:42,317 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:22:42,324 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:42,325 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:42,329 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,332 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:22:42,333 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:22:42,333 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:22:42,333 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:22:42,334 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:22:42,335 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:22:42,336 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:22:42,337 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:22:42,337 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:22:42,337 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:22:42,458 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:22:42,511 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:42,511 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:42,511 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:42,524 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,524 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:42,525 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:42,529 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:42,529 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:42,533 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,539 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:22:42,539 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:22:42,539 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:22:42,539 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:22:42,544 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,544 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:22:42,544 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:22:42,544 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:22:42,545 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:22:42,549 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,549 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:22:42,549 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:42,550 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:42,551 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:42,555 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:42,555 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:42,563 - app.utils.memory_management - INFO - Memory before cleanup: 492.28 MB
2025-05-26 12:22:42,687 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:22:42,687 - app.utils.memory_management - INFO - Memory after cleanup: 492.28 MB (freed 0.00 MB)
2025-05-26 12:22:47,544 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:22:47,553 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:47,554 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:47,554 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:47,561 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,561 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:47,562 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:22:47,570 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:47,583 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:22:47,588 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:47,588 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:47,592 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,596 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:22:47,596 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:22:47,596 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:22:47,597 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:22:47,597 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:22:47,598 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:22:47,599 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:22:47,599 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:22:47,600 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:22:47,601 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:22:47,726 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:22:47,745 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:47,746 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:47,746 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:47,752 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,752 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:47,752 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:47,758 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:47,759 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:47,763 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,767 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:22:47,767 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:22:47,767 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:22:47,768 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:22:47,778 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,779 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:22:47,779 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:22:47,779 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:22:47,779 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:22:47,783 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,783 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:22:47,784 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:47,784 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:47,784 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:47,788 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:47,789 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:47,795 - app.utils.memory_management - INFO - Memory before cleanup: 492.30 MB
2025-05-26 12:22:47,940 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:22:47,940 - app.utils.memory_management - INFO - Memory after cleanup: 492.30 MB (freed 0.00 MB)
2025-05-26 12:22:56,554 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:22:56,569 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:56,570 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:56,571 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:56,576 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,577 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:56,577 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:22:56,590 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:22:56,597 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:56,597 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:56,602 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,606 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:22:56,607 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:22:56,608 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:22:56,608 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:22:56,734 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:22:56,753 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:56,754 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:56,754 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:56,760 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,760 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:56,761 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:22:56,767 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:22:56,767 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:22:56,772 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,777 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:22:56,777 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:22:56,778 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:22:56,778 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:22:56,787 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,788 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:22:56,788 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:22:56,788 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:22:56,789 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:22:56,790 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,790 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:22:56,790 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:22:56,790 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:22:56,790 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:22:56,799 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:22:56,800 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:22:56,807 - app.utils.memory_management - INFO - Memory before cleanup: 492.28 MB
2025-05-26 12:22:56,923 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:22:56,954 - app.utils.memory_management - INFO - Memory after cleanup: 492.28 MB (freed 0.00 MB)
2025-05-26 12:23:04,997 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 12:23:05,012 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:23:05,012 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:23:05,013 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:23:05,019 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,020 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:23:05,020 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 12:23:05,029 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:23:05,039 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 12:23:05,047 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:23:05,048 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:23:05,053 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,056 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 12:23:05,056 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 12:23:05,057 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 12:23:05,059 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 12:23:05,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 12:23:05,061 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 12:23:05,062 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 12:23:05,063 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 12:23:05,063 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 12:23:05,064 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 12:23:05,189 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 12:23:05,210 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:23:05,210 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:23:05,211 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:23:05,216 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,216 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:23:05,216 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 12:23:05,227 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 12:23:05,227 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 12:23:05,231 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,236 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 12:23:05,236 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 12:23:05,236 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 12:23:05,237 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 12:23:05,241 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,242 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 12:23:05,242 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 12:23:05,243 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 12:23:05,243 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 12:23:05,247 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,247 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 12:23:05,248 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 12:23:05,248 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 12:23:05,248 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 12:23:05,252 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 12:23:05,253 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 12:23:05,260 - app.utils.memory_management - INFO - Memory before cleanup: 492.30 MB
2025-05-26 12:23:05,376 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 12:23:05,377 - app.utils.memory_management - INFO - Memory after cleanup: 492.30 MB (freed 0.00 MB)
2025-05-26 13:11:04,056 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:04,066 - app.utils.session_state - INFO - Initializing session state
2025-05-26 13:11:04,067 - app.utils.session_state - INFO - Session state initialized
2025-05-26 13:11:04,077 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 13:11:04,089 - app.utils.memory_management - INFO - Memory before cleanup: 490.44 MB
2025-05-26 13:11:04,243 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-05-26 13:11:04,244 - app.utils.memory_management - INFO - Memory after cleanup: 490.44 MB (freed 0.00 MB)
2025-05-26 13:11:20,206 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:20,206 - app.utils.memory_management - INFO - Memory before cleanup: 491.34 MB
2025-05-26 13:11:20,338 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-26 13:11:20,338 - app.utils.memory_management - INFO - Memory after cleanup: 491.34 MB (freed 0.00 MB)
2025-05-26 13:11:21,444 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:21,468 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 13:11:21,469 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 13:11:21,469 - app - INFO - Data shape: (576, 6)
2025-05-26 13:11:21,469 - app - INFO - File COMI contains 2025 data
2025-05-26 13:11:21,492 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-26 13:11:21,492 - app - INFO - Features shape: (576, 36)
2025-05-26 13:11:21,501 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-26 13:11:21,502 - app - INFO - Date range: 2023-01-02 to 2025-05-22
2025-05-26 13:11:21,502 - app - INFO - Data shape: (576, 6)
2025-05-26 13:11:21,502 - app - INFO - File COMI contains 2025 data
2025-05-26 13:11:21,508 - app.utils.memory_management - INFO - Memory before cleanup: 491.96 MB
2025-05-26 13:11:21,622 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-05-26 13:11:21,622 - app.utils.memory_management - INFO - Memory after cleanup: 491.96 MB (freed 0.00 MB)
2025-05-26 13:11:21,757 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:21,782 - app.utils.memory_management - INFO - Memory before cleanup: 492.04 MB
2025-05-26 13:11:21,906 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-26 13:11:21,907 - app.utils.memory_management - INFO - Memory after cleanup: 492.04 MB (freed 0.00 MB)
2025-05-26 13:11:25,558 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:25,570 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:25,570 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:25,571 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:25,576 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,576 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:25,576 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:11:25,576 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:11:25,599 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:25,601 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:25,606 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,612 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:11:25,613 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:11:25,613 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:11:25,613 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:11:25,613 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:11:25,614 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:11:25,616 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:11:25,616 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:11:25,616 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:11:25,617 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:11:25,738 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:11:25,764 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:25,764 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:25,764 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:25,776 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,776 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:25,777 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:11:25,783 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:25,783 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:25,787 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,792 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:11:25,792 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:11:25,794 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:11:25,794 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:11:25,799 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,799 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:11:25,799 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:11:25,800 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:11:25,800 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:11:25,804 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,804 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:11:25,805 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:25,805 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:25,805 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:25,809 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:25,810 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:25,811 - app.utils.memory_management - INFO - Memory before cleanup: 492.04 MB
2025-05-26 13:11:25,937 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:11:25,938 - app.utils.memory_management - INFO - Memory after cleanup: 492.04 MB (freed 0.00 MB)
2025-05-26 13:11:45,182 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:45,182 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:45,195 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:45,196 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:45,200 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,201 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:45,201 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:11:45,211 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:11:45,220 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:45,220 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:45,224 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,231 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:11:45,231 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:11:45,232 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:11:45,232 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:11:45,232 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:11:45,233 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:11:45,234 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:11:45,234 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:11:45,234 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:11:45,235 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:11:45,396 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:11:45,426 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:45,427 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:45,429 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:45,433 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,434 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:45,434 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:11:45,438 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:45,438 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:45,442 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,451 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:11:45,452 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:11:45,453 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:11:45,453 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:11:45,457 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,457 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:11:45,458 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:11:45,458 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:11:45,458 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:11:45,462 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,462 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:11:45,463 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:45,463 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:45,463 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:45,468 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:45,469 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:45,475 - app.utils.memory_management - INFO - Memory before cleanup: 492.04 MB
2025-05-26 13:11:45,591 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:11:45,591 - app.utils.memory_management - INFO - Memory after cleanup: 492.04 MB (freed 0.00 MB)
2025-05-26 13:11:48,340 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:11:48,355 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:48,355 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:48,355 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:48,362 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,362 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:48,363 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:11:48,371 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:11:48,383 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:11:48,385 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:48,385 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:48,385 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,430 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:11:48,433 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:11:48,435 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:11:48,435 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:11:48,435 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:11:48,436 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:11:48,438 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:11:48,438 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:11:48,438 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:11:48,438 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:11:48,562 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:11:48,583 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:48,583 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:48,584 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:48,591 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,591 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:48,592 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:11:48,600 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:11:48,600 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:11:48,604 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,608 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:11:48,609 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:11:48,609 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:11:48,609 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:11:48,613 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:11:48,615 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:11:48,634 - app.utils.memory_management - INFO - Memory before cleanup: 492.07 MB
2025-05-26 13:11:48,748 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:11:48,748 - app.utils.memory_management - INFO - Memory after cleanup: 492.07 MB (freed 0.00 MB)
2025-05-26 13:12:14,153 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:12:14,163 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:14,164 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:14,165 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:14,171 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:12:14,183 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:12:14,191 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:14,191 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:14,195 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,198 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:12:14,199 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:12:14,199 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:12:14,200 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:12:14,200 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:12:14,201 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:12:14,202 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:12:14,203 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:12:14,203 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:12:14,203 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:12:14,325 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:12:14,344 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:14,344 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:14,344 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:14,353 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,353 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:14,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:12:14,356 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:12:14,372 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:12:14,377 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,377 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:12:14,378 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:12:14,378 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:12:14,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:12:14,382 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,383 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:12:14,383 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:14,383 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:14,383 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:14,388 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:14,389 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:14,397 - app.utils.memory_management - INFO - Memory before cleanup: 492.06 MB
2025-05-26 13:12:14,515 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:12:14,515 - app.utils.memory_management - INFO - Memory after cleanup: 492.06 MB (freed 0.00 MB)
2025-05-26 13:12:22,899 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:12:22,915 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:22,915 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:22,915 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:22,922 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:22,922 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:22,923 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:12:22,932 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:12:22,943 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:12:22,949 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:22,949 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:22,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:22,957 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:12:22,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:12:22,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:12:22,958 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:12:22,959 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:12:22,959 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:12:22,960 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:12:22,961 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:12:22,961 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:12:22,961 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:12:23,086 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:12:23,101 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:23,101 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:23,101 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:23,121 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:23,121 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:23,122 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:12:23,127 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:23,127 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:23,132 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:23,136 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:12:23,137 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:12:23,137 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:12:23,137 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:12:23,146 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:23,146 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:12:23,146 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:12:23,148 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:12:23,149 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:12:23,154 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:23,155 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:12:23,155 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:23,155 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:23,156 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:23,160 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:23,160 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:23,169 - app.utils.memory_management - INFO - Memory before cleanup: 492.09 MB
2025-05-26 13:12:23,316 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:12:23,316 - app.utils.memory_management - INFO - Memory after cleanup: 492.09 MB (freed 0.00 MB)
2025-05-26 13:12:34,978 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:12:34,990 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:34,991 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:34,993 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:34,998 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:34,998 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:34,999 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:12:35,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:12:35,019 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:35,019 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:35,023 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:12:35,031 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:12:35,031 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:12:35,159 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:12:35,180 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:35,180 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:35,180 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:35,196 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,196 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:35,197 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:12:35,201 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:12:35,202 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:12:35,206 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,212 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:12:35,212 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:12:35,213 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:12:35,213 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:12:35,217 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,217 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:12:35,218 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:12:35,218 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:12:35,218 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:12:35,222 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,223 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:12:35,223 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:12:35,223 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:12:35,224 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:12:35,232 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:12:35,232 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:12:35,240 - app.utils.memory_management - INFO - Memory before cleanup: 492.08 MB
2025-05-26 13:12:35,368 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:12:35,371 - app.utils.memory_management - INFO - Memory after cleanup: 492.08 MB (freed 0.00 MB)
2025-05-26 13:16:47,159 - app - INFO - Using TensorFlow-based LSTM model
2025-05-26 13:16:47,197 - app - INFO - Found 8 stock files in data/stocks
2025-05-26 13:16:47,203 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:16:47,206 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:16:47,206 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:16:47,211 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,212 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:16:47,212 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-26 13:16:47,227 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:16:47,282 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-26 13:16:47,295 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:16:47,295 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:16:47,303 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,309 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-26 13:16:47,310 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-26 13:16:47,310 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-26 13:16:47,311 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-26 13:16:47,311 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-26 13:16:47,312 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-26 13:16:47,314 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-26 13:16:47,315 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-26 13:16:47,315 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-26 13:16:47,315 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-26 13:16:47,478 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-26 13:16:47,502 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:16:47,506 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:16:47,507 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:16:47,508 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,508 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:16:47,508 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-26 13:16:47,508 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-26 13:16:47,508 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-26 13:16:47,520 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,527 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-26 13:16:47,528 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-26 13:16:47,528 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-26 13:16:47,528 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-26 13:16:47,532 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,533 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-26 13:16:47,533 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-26 13:16:47,534 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-26 13:16:47,534 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-26 13:16:47,539 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,540 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-26 13:16:47,540 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-26 13:16:47,540 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-26 13:16:47,541 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-26 13:16:47,545 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-26 13:16:47,545 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-26 13:16:47,555 - app.utils.memory_management - INFO - Memory before cleanup: 492.13 MB
2025-05-26 13:16:47,673 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-26 13:16:47,673 - app.utils.memory_management - INFO - Memory after cleanup: 492.13 MB (freed 0.00 MB)
2025-05-26 13:23:51,112 - app - INFO - Cleaning up resources...
2025-05-26 13:23:51,113 - app.utils.memory_management - INFO - Memory before cleanup: 453.70 MB
2025-05-26 13:23:51,277 - app.utils.memory_management - INFO - Garbage collection: collected 1036 objects
2025-05-26 13:23:51,277 - app.utils.memory_management - INFO - Memory after cleanup: 453.33 MB (freed 0.37 MB)
2025-05-26 13:23:51,277 - app - INFO - Application shutdown complete
