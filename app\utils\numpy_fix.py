"""
This module provides a fix for the 'numpy._core' import error that can occur
with certain versions of numpy and scikit-learn.
"""

import sys
import logging
import importlib
import types

logger = logging.getLogger(__name__)

def fix_numpy_imports():
    """
    Apply a fix for the 'numpy._core' import error by adding a mock module
    to sys.modules if needed. Also handles numpy._core.multiarray and other
    submodules that might be missing.
    """
    # First, make sure numpy is imported
    import numpy

    # Fix for numpy._core
    try:
        # Try to import numpy._core to see if it exists
        import numpy._core
        logger.info("numpy._core module exists, no fix needed")
    except ImportError:
        logger.warning("numpy._core module not found, applying fix")

        # Create a mock module for numpy._core
        mock_core = types.ModuleType('numpy._core')
        sys.modules['numpy._core'] = mock_core

        # Try to import key components that might be needed
        try:
            from numpy import core as numpy_core
            # Copy attributes from numpy.core to our mock module
            for attr_name in dir(numpy_core):
                if not attr_name.startswith('__'):
                    setattr(mock_core, attr_name, getattr(numpy_core, attr_name))
            logger.info("Successfully copied numpy.core attributes to mock module")
        except Exception as e:
            logger.error(f"Error setting up mock numpy._core module: {str(e)}")

    # Fix for numpy._core.multiarray
    try:
        # Try to import numpy._core.multiarray to see if it exists
        import numpy._core.multiarray
        logger.info("numpy._core.multiarray module exists, no fix needed")
    except ImportError:
        logger.warning("numpy._core.multiarray module not found, applying fix")

        # Create a mock module for numpy._core.multiarray
        mock_multiarray = types.ModuleType('numpy._core.multiarray')
        sys.modules['numpy._core.multiarray'] = mock_multiarray

        # Try to import numpy.core.multiarray and copy its attributes
        try:
            # First check if numpy.core.multiarray exists
            multiarray_module = None

            # Try different possible paths for multiarray
            possible_paths = [
                'numpy.core.multiarray',
                'numpy.core._multiarray_umath',
                'numpy.core._multiarray_tests'
            ]

            for path in possible_paths:
                try:
                    multiarray_module = importlib.import_module(path)
                    logger.info(f"Found multiarray at {path}")
                    break
                except ImportError:
                    continue

            if multiarray_module:
                # Copy attributes from the found module to our mock module
                for attr_name in dir(multiarray_module):
                    if not attr_name.startswith('__'):
                        try:
                            attr_value = getattr(multiarray_module, attr_name)
                            setattr(mock_multiarray, attr_name, attr_value)
                        except (AttributeError, ImportError):
                            # Skip attributes that can't be copied
                            pass

                # Also add the module to numpy._core if it exists
                if 'numpy._core' in sys.modules:
                    setattr(sys.modules['numpy._core'], 'multiarray', mock_multiarray)

                logger.info("Successfully created mock numpy._core.multiarray module")
            else:
                # If we can't find multiarray, create basic functionality
                logger.warning("Could not find numpy multiarray module, creating basic mock")

                # Create some basic functions that might be needed
                # These are just placeholders that return appropriate types
                def mock_array(obj, dtype=None, *args, **kwargs):
                    return numpy.array(obj, dtype=dtype, *args, **kwargs)

                mock_multiarray.array = mock_array
                mock_multiarray.ndarray = numpy.ndarray

                # Add other common attributes
                mock_multiarray.dtype = numpy.dtype
                mock_multiarray.zeros = numpy.zeros
                mock_multiarray.ones = numpy.ones

                # Also add the module to numpy._core if it exists
                if 'numpy._core' in sys.modules:
                    setattr(sys.modules['numpy._core'], 'multiarray', mock_multiarray)

        except Exception as e:
            logger.error(f"Error setting up mock numpy._core.multiarray module: {str(e)}")

    # Fix for numpy._core._multiarray_umath
    try:
        import numpy._core._multiarray_umath
        logger.info("numpy._core._multiarray_umath module exists, no fix needed")
    except ImportError:
        logger.warning("numpy._core._multiarray_umath module not found, applying fix")

        # Create a mock module
        mock_multiarray_umath = types.ModuleType('numpy._core._multiarray_umath')
        sys.modules['numpy._core._multiarray_umath'] = mock_multiarray_umath

        # Try to import the real module from numpy.core
        try:
            # Check if numpy.core._multiarray_umath exists
            try:
                from numpy.core import _multiarray_umath

                # Copy attributes
                for attr_name in dir(_multiarray_umath):
                    if not attr_name.startswith('__'):
                        try:
                            setattr(mock_multiarray_umath, attr_name, getattr(_multiarray_umath, attr_name))
                        except (AttributeError, ImportError):
                            pass

                logger.info("Successfully created mock numpy._core._multiarray_umath module")
            except ImportError:
                logger.warning("Could not find numpy.core._multiarray_umath, using basic mock")

                # Add basic functionality
                mock_multiarray_umath.ndarray = numpy.ndarray
                mock_multiarray_umath.dtype = numpy.dtype

                # Add common ufuncs
                for ufunc_name in ['add', 'subtract', 'multiply', 'divide', 'power']:
                    if hasattr(numpy, ufunc_name):
                        setattr(mock_multiarray_umath, ufunc_name, getattr(numpy, ufunc_name))

        except Exception as e:
            logger.error(f"Error setting up mock numpy._core._multiarray_umath module: {str(e)}")

    # Fix for numpy.random._mt19937.MT19937 BitGenerator issue
    try:
        # Check if numpy.random._mt19937 exists
        import numpy.random._mt19937
        logger.info("numpy.random._mt19937 module exists, no fix needed")

        # Even if the module exists, we need to make sure MT19937 is properly registered as a BitGenerator
        try:
            # Check if MT19937 is properly registered
            import numpy.random
            from numpy.random import Generator

            # Create a test generator to see if MT19937 works
            test_mt = numpy.random._mt19937.MT19937(42)
            test_gen = Generator(test_mt)
            _ = test_gen.random()  # This will fail if MT19937 is not properly registered

            logger.info("MT19937 is properly registered as a BitGenerator")
        except Exception as e:
            logger.warning(f"MT19937 exists but is not properly registered: {str(e)}")

            # Fix the registration
            try:
                import numpy.random

                # Create a proper BitGenerator base class if needed
                if not hasattr(numpy.random, 'BitGenerator'):
                    class BitGenerator:
                        def __init__(self, seed=None):
                            self.seed = seed if seed is not None else 0

                        def __repr__(self):
                            return "BitGenerator()"

                    numpy.random.BitGenerator = BitGenerator

                # Make MT19937 inherit from BitGenerator
                if hasattr(numpy.random._mt19937, 'MT19937'):
                    # Save the original class
                    OriginalMT19937 = numpy.random._mt19937.MT19937

                    # Create a new class that inherits from BitGenerator
                    class FixedMT19937(numpy.random.BitGenerator):
                        def __init__(self, seed=None):
                            self.seed = seed if seed is not None else 0
                            self.state = {"state": self.seed}
                            self._original = OriginalMT19937(seed)

                        def jumped(self, *args, **kwargs):
                            return self

                        def __repr__(self):
                            return "FixedMT19937()"

                        # Add methods that might be needed by numpy.random.Generator
                        def next_uint64(self):
                            import random
                            return random.randint(0, 2**64-1)

                        def next_uint32(self):
                            import random
                            return random.randint(0, 2**32-1)

                        def next_double(self):
                            import random
                            return random.random()

                    # Replace the original class
                    numpy.random._mt19937.MT19937 = FixedMT19937
                    numpy.random.MT19937 = FixedMT19937

                    # Register the fixed class with BitGenerator if possible
                    if hasattr(numpy.random, '_generator') and hasattr(numpy.random._generator, 'BitGenerator'):
                        try:
                            numpy.random._generator.BitGenerator.register(FixedMT19937)
                            logger.info("Registered FixedMT19937 with BitGenerator")
                        except Exception as reg_err:
                            logger.warning(f"Could not register FixedMT19937: {str(reg_err)}")

                    logger.info("Successfully fixed MT19937 BitGenerator registration")
            except Exception as reg_error:
                logger.error(f"Error fixing MT19937 registration: {str(reg_error)}")
    except ImportError:
        logger.warning("numpy.random._mt19937 module not found, applying fix")

        # Create a mock module for numpy.random._mt19937
        mock_mt19937 = types.ModuleType('numpy.random._mt19937')
        sys.modules['numpy.random._mt19937'] = mock_mt19937

        # Try to create a mock MT19937 class
        try:
            # First check if numpy.random has a BitGenerator or SeedSequence
            import numpy.random

            # Create BitGenerator if it doesn't exist
            if not hasattr(numpy.random, 'BitGenerator'):
                class BitGenerator:
                    def __init__(self, seed=None):
                        self.seed = seed if seed is not None else 0

                    def __repr__(self):
                        return "BitGenerator()"

                numpy.random.BitGenerator = BitGenerator

            # Create a mock MT19937 class that inherits from BitGenerator
            class MockMT19937(numpy.random.BitGenerator):
                def __init__(self, seed=None):
                    self.seed = seed if seed is not None else 0
                    self.state = {"state": self.seed}

                def jumped(self, *args, **kwargs):
                    return self

                def __repr__(self):
                    return "MockMT19937()"

                # Add methods that might be needed by numpy.random.Generator
                def next_uint64(self):
                    import random
                    return random.randint(0, 2**64-1)

                def next_uint32(self):
                    import random
                    return random.randint(0, 2**32-1)

                def next_double(self):
                    import random
                    return random.random()

            # Add the mock class to the module
            mock_mt19937.MT19937 = MockMT19937

            # Also register it with numpy.random
            numpy.random.MT19937 = MockMT19937

            # Make sure it's recognized as a BitGenerator
            if hasattr(numpy.random, '_generator'):
                if hasattr(numpy.random._generator, 'BitGenerator'):
                    try:
                        numpy.random._generator.BitGenerator.register(MockMT19937)
                        logger.info("Registered MockMT19937 with BitGenerator")
                    except Exception as reg_err:
                        logger.warning(f"Could not register MockMT19937: {str(reg_err)}")

            # Create a Generator that uses our MockMT19937
            if hasattr(numpy.random, 'Generator'):
                try:
                    test_gen = numpy.random.Generator(MockMT19937(42))
                    _ = test_gen.random()  # Test if it works
                    logger.info("Successfully tested MockMT19937 with Generator")
                except Exception as gen_error:
                    logger.warning(f"Error testing MockMT19937 with Generator: {str(gen_error)}")

            logger.info("Successfully created mock MT19937 BitGenerator")
        except Exception as e:
            logger.error(f"Error setting up mock MT19937 BitGenerator: {str(e)}")

            # Create a simpler mock if the above fails
            try:
                # Create a basic class that can be instantiated
                class BasicMT19937:
                    def __init__(self, seed=None):
                        self.seed = seed if seed is not None else 0

                    def jumped(self, *args, **kwargs):
                        return self

                    def __repr__(self):
                        return "BasicMT19937()"

                    # Add methods that might be needed
                    def next_uint64(self):
                        import random
                        return random.randint(0, 2**64-1)

                    def next_uint32(self):
                        import random
                        return random.randint(0, 2**32-1)

                    def next_double(self):
                        import random
                        return random.random()

                # Add the basic class to the module
                mock_mt19937.MT19937 = BasicMT19937

                # Also add it to numpy.random
                import numpy.random
                numpy.random.MT19937 = BasicMT19937

                logger.info("Created basic mock MT19937 class")
            except Exception as e2:
                logger.error(f"Error creating basic MT19937 mock: {str(e2)}")

    # Fix for numpy.random._bit_generator
    try:
        import numpy.random._bit_generator  # type: ignore
        logger.info("numpy.random._bit_generator module exists, no fix needed")
    except ImportError:
        logger.warning("numpy.random._bit_generator module not found, applying fix")

        # Create a mock module
        mock_bit_generator = types.ModuleType('numpy.random._bit_generator')
        sys.modules['numpy.random._bit_generator'] = mock_bit_generator

        # Try to create a mock BitGenerator class
        try:
            # Create a basic BitGenerator class
            class MockBitGenerator:
                def __init__(self, seed=None):
                    self.seed = seed if seed is not None else 0

                def __repr__(self):
                    return "MockBitGenerator()"

            # Add the class to the module
            mock_bit_generator.BitGenerator = MockBitGenerator

            # Also add it to numpy.random
            import numpy.random
            if not hasattr(numpy.random, 'BitGenerator'):
                numpy.random.BitGenerator = MockBitGenerator

            logger.info("Created mock BitGenerator class")
        except Exception as e:
            logger.error(f"Error creating mock BitGenerator: {str(e)}")

    logger.info("numpy import fixes applied")
