"""
Advanced Technical Analysis Component with AI-Powered Features

This module provides sophisticated technical analysis capabilities including:
- AI-powered pattern recognition
- Market microstructure indicators
- Volume profile analysis
- Options flow analysis
- Intermarket analysis
- Custom indicator creation
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

# Import existing utilities
from app.utils.technical_indicators import TechnicalIndicators
from app.utils.common import load_stock_data

class AdvancedTechnicalAnalysis:
    """
    Advanced Technical Analysis with AI-powered features
    """

    def __init__(self):
        self.patterns_detected = []
        self.custom_indicators = {}
        self.volume_profile_data = {}

    @staticmethod
    def detect_ai_patterns(data: pd.DataFrame, pattern_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        AI-powered pattern detection using advanced algorithms

        Args:
            data (pd.DataFrame): Historical price data
            pattern_types (List[str]): Types of patterns to detect

        Returns:
            List[Dict[str, Any]]: Detected patterns with confidence scores
        """
        if pattern_types is None:
            pattern_types = [
                "Head and Shoulders", "Inverse Head and Shoulders",
                "Double Top", "Double Bottom", "Triple Top", "Triple Bottom",
                "Ascending Triangle", "Descending Triangle", "Symmetrical Triangle",
                "Cup and Handle", "Rounding Bottom", "Wedge", "Flag", "Pennant",
                "Rectangle", "Channel", "Breakout", "Support", "Resistance"
            ]

        detected_patterns = []

        try:
            # Prepare data for pattern detection
            highs = data['High'].values
            lows = data['Low'].values
            closes = data['Close'].values
            volumes = data.get('Volume', pd.Series([0] * len(data))).values

            # Head and Shoulders Pattern Detection
            if "Head and Shoulders" in pattern_types:
                patterns = AdvancedTechnicalAnalysis._detect_head_and_shoulders(highs, lows, closes)
                detected_patterns.extend(patterns)

            # Double Top/Bottom Detection
            if "Double Top" in pattern_types or "Double Bottom" in pattern_types:
                patterns = AdvancedTechnicalAnalysis._detect_double_patterns(highs, lows, closes)
                detected_patterns.extend(patterns)

            # Triangle Patterns
            if any("Triangle" in pt for pt in pattern_types):
                patterns = AdvancedTechnicalAnalysis._detect_triangle_patterns(highs, lows, closes)
                detected_patterns.extend(patterns)

            # Support and Resistance Levels
            if "Support" in pattern_types or "Resistance" in pattern_types:
                patterns = AdvancedTechnicalAnalysis._detect_support_resistance(highs, lows, closes)
                detected_patterns.extend(patterns)

            # Breakout Patterns
            if "Breakout" in pattern_types:
                patterns = AdvancedTechnicalAnalysis._detect_breakouts(highs, lows, closes, volumes)
                detected_patterns.extend(patterns)

        except Exception as e:
            logger.error(f"Error in AI pattern detection: {str(e)}")

        return detected_patterns

    @staticmethod
    def _detect_head_and_shoulders(highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> List[Dict[str, Any]]:
        """Detect Head and Shoulders patterns using AI algorithms"""
        patterns = []
        min_pattern_length = 20

        if len(highs) < min_pattern_length:
            return patterns

        # Find local maxima (potential shoulders and head)
        from scipy.signal import find_peaks

        peaks, _ = find_peaks(highs, distance=5, prominence=np.std(highs) * 0.5)

        if len(peaks) >= 3:
            for i in range(len(peaks) - 2):
                left_shoulder = peaks[i]
                head = peaks[i + 1]
                right_shoulder = peaks[i + 2]

                # Check if it forms a head and shoulders pattern
                left_height = highs[left_shoulder]
                head_height = highs[head]
                right_height = highs[right_shoulder]

                # Head should be higher than both shoulders
                if (head_height > left_height and head_height > right_height and
                    abs(left_height - right_height) / max(left_height, right_height) < 0.05):

                    # Calculate neckline
                    neckline_left = np.min(lows[left_shoulder:head])
                    neckline_right = np.min(lows[head:right_shoulder])
                    neckline = (neckline_left + neckline_right) / 2

                    # Calculate confidence based on pattern quality
                    height_ratio = head_height / max(left_height, right_height)
                    symmetry = 1 - abs(left_height - right_height) / max(left_height, right_height)
                    confidence = min(95, (height_ratio - 1) * 100 + symmetry * 50)

                    patterns.append({
                        "name": "Head and Shoulders",
                        "type": "reversal",
                        "direction": "bearish",
                        "confidence": max(60, confidence),
                        "start_idx": left_shoulder,
                        "end_idx": right_shoulder,
                        "key_levels": {
                            "left_shoulder": left_shoulder,
                            "head": head,
                            "right_shoulder": right_shoulder,
                            "neckline": neckline
                        },
                        "target_price": neckline - (head_height - neckline),
                        "description": "Bearish reversal pattern with head higher than shoulders"
                    })

        return patterns

    @staticmethod
    def _detect_double_patterns(highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> List[Dict[str, Any]]:
        """Detect Double Top and Double Bottom patterns"""
        patterns = []

        # Double Top Detection
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(highs, distance=10, prominence=np.std(highs) * 0.3)

        if len(peaks) >= 2:
            for i in range(len(peaks) - 1):
                peak1 = peaks[i]
                peak2 = peaks[i + 1]

                height1 = highs[peak1]
                height2 = highs[peak2]

                # Check if heights are similar (within 3%)
                if abs(height1 - height2) / max(height1, height2) < 0.03:
                    # Find the valley between peaks
                    valley_idx = peak1 + np.argmin(lows[peak1:peak2])
                    valley_price = lows[valley_idx]

                    # Calculate confidence
                    height_similarity = 1 - abs(height1 - height2) / max(height1, height2)
                    depth = (min(height1, height2) - valley_price) / min(height1, height2)
                    confidence = min(95, height_similarity * 50 + depth * 100)

                    patterns.append({
                        "name": "Double Top",
                        "type": "reversal",
                        "direction": "bearish",
                        "confidence": max(65, confidence),
                        "start_idx": peak1,
                        "end_idx": peak2,
                        "key_levels": {
                            "peak1": peak1,
                            "peak2": peak2,
                            "valley": valley_idx,
                            "support": valley_price
                        },
                        "target_price": valley_price - (min(height1, height2) - valley_price),
                        "description": "Bearish reversal pattern with two similar peaks"
                    })

        # Double Bottom Detection
        troughs, _ = find_peaks(-lows, distance=10, prominence=np.std(lows) * 0.3)

        if len(troughs) >= 2:
            for i in range(len(troughs) - 1):
                trough1 = troughs[i]
                trough2 = troughs[i + 1]

                depth1 = lows[trough1]
                depth2 = lows[trough2]

                # Check if depths are similar (within 3%)
                if abs(depth1 - depth2) / max(depth1, depth2) < 0.03:
                    # Find the peak between troughs
                    peak_idx = trough1 + np.argmax(highs[trough1:trough2])
                    peak_price = highs[peak_idx]

                    # Calculate confidence
                    depth_similarity = 1 - abs(depth1 - depth2) / max(depth1, depth2)
                    height = (peak_price - max(depth1, depth2)) / max(depth1, depth2)
                    confidence = min(95, depth_similarity * 50 + height * 100)

                    patterns.append({
                        "name": "Double Bottom",
                        "type": "reversal",
                        "direction": "bullish",
                        "confidence": max(65, confidence),
                        "start_idx": trough1,
                        "end_idx": trough2,
                        "key_levels": {
                            "trough1": trough1,
                            "trough2": trough2,
                            "peak": peak_idx,
                            "resistance": peak_price
                        },
                        "target_price": peak_price + (peak_price - max(depth1, depth2)),
                        "description": "Bullish reversal pattern with two similar troughs"
                    })

        return patterns

    @staticmethod
    def _detect_triangle_patterns(highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> List[Dict[str, Any]]:
        """Detect Triangle patterns (Ascending, Descending, Symmetrical)"""
        patterns = []
        min_length = 20

        if len(highs) < min_length:
            return patterns

        # Look for triangle patterns in the last portion of data
        for start_idx in range(len(highs) - min_length, max(0, len(highs) - 50), -5):
            end_idx = len(highs) - 1
            segment_highs = highs[start_idx:end_idx]
            segment_lows = lows[start_idx:end_idx]

            if len(segment_highs) < min_length:
                continue

            # Calculate trend lines
            x = np.arange(len(segment_highs))

            # Upper trend line (resistance)
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(segment_highs, distance=3)
            if len(peaks) >= 2:
                upper_slope, upper_intercept = np.polyfit(peaks, segment_highs[peaks], 1)
            else:
                continue

            # Lower trend line (support)
            troughs, _ = find_peaks(-segment_lows, distance=3)
            if len(troughs) >= 2:
                lower_slope, lower_intercept = np.polyfit(troughs, segment_lows[troughs], 1)
            else:
                continue

            # Classify triangle type
            slope_threshold = np.std(segment_highs) * 0.01

            if abs(upper_slope) < slope_threshold and lower_slope > slope_threshold:
                pattern_type = "Ascending Triangle"
                direction = "bullish"
            elif upper_slope < -slope_threshold and abs(lower_slope) < slope_threshold:
                pattern_type = "Descending Triangle"
                direction = "bearish"
            elif upper_slope < -slope_threshold and lower_slope > slope_threshold:
                pattern_type = "Symmetrical Triangle"
                direction = "neutral"
            else:
                continue

            # Calculate confidence based on how well points fit the trend lines
            upper_line = upper_slope * x + upper_intercept
            lower_line = lower_slope * x + lower_intercept

            # Check convergence
            convergence_point = (lower_intercept - upper_intercept) / (upper_slope - lower_slope)
            if convergence_point > len(segment_highs) * 2:  # Too far in future
                continue

            confidence = min(90, 70 + (min_length / len(segment_highs)) * 20)

            patterns.append({
                "name": pattern_type,
                "type": "continuation" if pattern_type == "Symmetrical Triangle" else "breakout",
                "direction": direction,
                "confidence": confidence,
                "start_idx": start_idx,
                "end_idx": end_idx,
                "key_levels": {
                    "upper_slope": upper_slope,
                    "lower_slope": lower_slope,
                    "convergence_point": convergence_point
                },
                "description": f"{pattern_type} pattern indicating potential {direction} movement"
            })

            break  # Only detect one triangle pattern

        return patterns

    @staticmethod
    def _detect_support_resistance(highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> List[Dict[str, Any]]:
        """Detect Support and Resistance levels using clustering"""
        patterns = []

        try:
            from sklearn.cluster import KMeans

            # Combine all price points
            all_prices = np.concatenate([highs, lows, closes])
            price_counts = {}

            # Count price occurrences (rounded to reduce noise)
            for price in all_prices:
                rounded_price = round(price, 2)
                price_counts[rounded_price] = price_counts.get(rounded_price, 0) + 1

            # Find significant price levels
            significant_prices = [price for price, count in price_counts.items() if count >= 3]

            if len(significant_prices) >= 2:
                # Use clustering to find support/resistance levels
                prices_array = np.array(significant_prices).reshape(-1, 1)
                n_clusters = min(5, len(significant_prices))

                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                clusters = kmeans.fit_predict(prices_array)
                centers = kmeans.cluster_centers_.flatten()

                current_price = closes[-1]

                for center in centers:
                    # Determine if it's support or resistance
                    if center < current_price:
                        level_type = "Support"
                        direction = "bullish"
                    else:
                        level_type = "Resistance"
                        direction = "bearish"

                    # Calculate strength based on how many times price touched this level
                    touches = sum(1 for price in all_prices if abs(price - center) / center < 0.01)
                    strength = min(95, touches * 15)

                    patterns.append({
                        "name": level_type,
                        "type": "level",
                        "direction": direction,
                        "confidence": max(60, strength),
                        "start_idx": 0,
                        "end_idx": len(closes) - 1,
                        "key_levels": {
                            "level": center,
                            "touches": touches,
                            "distance_from_current": abs(center - current_price) / current_price
                        },
                        "description": f"{level_type} level at {center:.2f} with {touches} touches"
                    })

        except Exception as e:
            logger.error(f"Error detecting support/resistance: {str(e)}")

        return patterns

    @staticmethod
    def _detect_breakouts(highs: np.ndarray, lows: np.ndarray, closes: np.ndarray, volumes: np.ndarray) -> List[Dict[str, Any]]:
        """Detect breakout patterns with volume confirmation"""
        patterns = []
        lookback = 20

        if len(closes) < lookback:
            return patterns

        try:
            # Calculate recent high and low
            recent_high = np.max(highs[-lookback:])
            recent_low = np.min(lows[-lookback:])
            current_price = closes[-1]

            # Calculate average volume
            avg_volume = np.mean(volumes[-lookback:]) if len(volumes) > 0 else 0
            current_volume = volumes[-1] if len(volumes) > 0 else 0

            # Check for breakouts
            if current_price > recent_high * 1.001:  # 0.1% above recent high
                volume_confirmation = current_volume > avg_volume * 1.5 if avg_volume > 0 else True
                confidence = 75 + (15 if volume_confirmation else 0)

                patterns.append({
                    "name": "Upward Breakout",
                    "type": "breakout",
                    "direction": "bullish",
                    "confidence": confidence,
                    "start_idx": len(closes) - lookback,
                    "end_idx": len(closes) - 1,
                    "key_levels": {
                        "breakout_level": recent_high,
                        "current_price": current_price,
                        "volume_confirmation": volume_confirmation,
                        "volume_ratio": current_volume / avg_volume if avg_volume > 0 else 1
                    },
                    "target_price": current_price + (current_price - recent_low),
                    "description": f"Bullish breakout above {recent_high:.2f}" +
                                 (" with volume confirmation" if volume_confirmation else "")
                })

            elif current_price < recent_low * 0.999:  # 0.1% below recent low
                volume_confirmation = current_volume > avg_volume * 1.5 if avg_volume > 0 else True
                confidence = 75 + (15 if volume_confirmation else 0)

                patterns.append({
                    "name": "Downward Breakout",
                    "type": "breakout",
                    "direction": "bearish",
                    "confidence": confidence,
                    "start_idx": len(closes) - lookback,
                    "end_idx": len(closes) - 1,
                    "key_levels": {
                        "breakout_level": recent_low,
                        "current_price": current_price,
                        "volume_confirmation": volume_confirmation,
                        "volume_ratio": current_volume / avg_volume if avg_volume > 0 else 1
                    },
                    "target_price": current_price - (recent_high - current_price),
                    "description": f"Bearish breakout below {recent_low:.2f}" +
                                 (" with volume confirmation" if volume_confirmation else "")
                })

        except Exception as e:
            logger.error(f"Error detecting breakouts: {str(e)}")

        return patterns

    @staticmethod
    def calculate_volume_profile(data: pd.DataFrame, bins: int = 50) -> Dict[str, Any]:
        """
        Calculate Volume Profile - shows volume traded at each price level

        Args:
            data (pd.DataFrame): Historical price and volume data
            bins (int): Number of price bins

        Returns:
            Dict[str, Any]: Volume profile data
        """
        try:
            if 'Volume' not in data.columns:
                return {"error": "Volume data not available"}

            # Get price range
            price_min = data['Low'].min()
            price_max = data['High'].max()

            # Create price bins
            price_bins = np.linspace(price_min, price_max, bins + 1)
            volume_profile = np.zeros(bins)

            # Calculate volume for each price level
            for _, row in data.iterrows():
                # Distribute volume across the price range for this bar
                low, high, volume = row['Low'], row['High'], row['Volume']

                # Find which bins this bar spans
                low_bin = np.digitize(low, price_bins) - 1
                high_bin = np.digitize(high, price_bins) - 1

                # Ensure bins are within range
                low_bin = max(0, min(low_bin, bins - 1))
                high_bin = max(0, min(high_bin, bins - 1))

                # Distribute volume evenly across the price range
                if low_bin == high_bin:
                    volume_profile[low_bin] += volume
                else:
                    bins_spanned = high_bin - low_bin + 1
                    volume_per_bin = volume / bins_spanned
                    for bin_idx in range(low_bin, high_bin + 1):
                        volume_profile[bin_idx] += volume_per_bin

            # Calculate key levels
            max_volume_idx = np.argmax(volume_profile)
            poc_price = (price_bins[max_volume_idx] + price_bins[max_volume_idx + 1]) / 2  # Point of Control

            # Calculate Value Area (70% of volume)
            total_volume = np.sum(volume_profile)
            target_volume = total_volume * 0.7

            # Find Value Area High and Low
            cumulative_volume = 0
            value_area_bins = []

            # Start from POC and expand outward
            left_idx = right_idx = max_volume_idx

            while cumulative_volume < target_volume and (left_idx > 0 or right_idx < bins - 1):
                # Add current bins to value area
                if left_idx >= 0:
                    cumulative_volume += volume_profile[left_idx]
                    value_area_bins.append(left_idx)
                if right_idx < bins and right_idx != left_idx:
                    cumulative_volume += volume_profile[right_idx]
                    value_area_bins.append(right_idx)

                # Expand to the side with more volume
                left_volume = volume_profile[left_idx - 1] if left_idx > 0 else 0
                right_volume = volume_profile[right_idx + 1] if right_idx < bins - 1 else 0

                if left_volume >= right_volume and left_idx > 0:
                    left_idx -= 1
                elif right_idx < bins - 1:
                    right_idx += 1
                else:
                    break

            value_area_low = price_bins[min(value_area_bins)]
            value_area_high = price_bins[max(value_area_bins) + 1]

            return {
                "price_bins": price_bins[:-1],  # Remove last bin edge
                "volume_profile": volume_profile,
                "poc_price": poc_price,
                "value_area_high": value_area_high,
                "value_area_low": value_area_low,
                "total_volume": total_volume,
                "max_volume": volume_profile[max_volume_idx]
            }

        except Exception as e:
            logger.error(f"Error calculating volume profile: {str(e)}")
            return {"error": str(e)}

    @staticmethod
    def calculate_market_microstructure_indicators(data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate market microstructure indicators

        Args:
            data (pd.DataFrame): Historical price and volume data

        Returns:
            Dict[str, Any]: Market microstructure indicators
        """
        try:
            indicators = {}

            # Volume Weighted Average Price (VWAP)
            if 'Volume' in data.columns:
                typical_price = (data['High'] + data['Low'] + data['Close']) / 3
                vwap = (typical_price * data['Volume']).cumsum() / data['Volume'].cumsum()
                indicators['VWAP'] = vwap.iloc[-1]

                # Volume Rate of Change
                volume_roc = data['Volume'].pct_change(periods=5).iloc[-1] * 100
                indicators['Volume_ROC_5'] = volume_roc

                # Accumulation/Distribution Line
                money_flow_multiplier = ((data['Close'] - data['Low']) - (data['High'] - data['Close'])) / (data['High'] - data['Low'])
                money_flow_volume = money_flow_multiplier * data['Volume']
                ad_line = money_flow_volume.cumsum()
                indicators['AD_Line'] = ad_line.iloc[-1]

                # Chaikin Money Flow
                cmf_period = min(20, len(data))
                cmf = money_flow_volume.rolling(cmf_period).sum() / data['Volume'].rolling(cmf_period).sum()
                indicators['CMF'] = cmf.iloc[-1]

            # Price-based indicators
            # Average True Range
            high_low = data['High'] - data['Low']
            high_close = np.abs(data['High'] - data['Close'].shift())
            low_close = np.abs(data['Low'] - data['Close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(14).mean().iloc[-1]
            indicators['ATR'] = atr

            # Price efficiency ratio
            price_change = abs(data['Close'].iloc[-1] - data['Close'].iloc[-20])
            price_volatility = true_range.rolling(20).sum().iloc[-1]
            efficiency_ratio = price_change / price_volatility if price_volatility > 0 else 0
            indicators['Efficiency_Ratio'] = efficiency_ratio

            # Bid-Ask Spread Proxy (using high-low spread)
            spread_proxy = (data['High'] - data['Low']) / data['Close']
            indicators['Spread_Proxy'] = spread_proxy.rolling(20).mean().iloc[-1]

            return indicators

        except Exception as e:
            logger.error(f"Error calculating microstructure indicators: {str(e)}")
            return {"error": str(e)}